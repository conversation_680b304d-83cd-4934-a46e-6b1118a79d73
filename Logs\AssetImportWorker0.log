Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.48f1 (b805b124c6b7) revision 12060081'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'zh' Physical Memory: 65372 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
G:/Projects/Fanpaiji/FQZS_Qianduan
-logFile
Logs/AssetImportWorker0.log
-srvPort
11922
Successfully changed project path to: G:/Projects/Fanpaiji/FQZS_Qianduan
G:/Projects/Fanpaiji/FQZS_Qianduan
Using Asset Import Pipeline V2.
Player connection [14268] Host "[IP] ************** [Port] 73 [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-KBSURNA) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [14268] Host "[IP] ************** [Port] 73 [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-KBSURNA) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

Refreshing native plugins compatible for Editor in 236.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1 (b805b124c6b7)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2020.3.48f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/Projects/Fanpaiji/FQZS_Qianduan/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon RX 5700 XT (ID=0x731f)
    Vendor:   
    VRAM:     8125 MB
    Driver:   32.0.12033.1030
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2020.3.48f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2020.3.48f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2020.3.48f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56488
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2020.3.48f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2020.3.48f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001336 seconds.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 269.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.655 seconds
Domain Reload Profiling:
	ReloadAssembly (656ms)
		BeginReloadAssembly (41ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (564ms)
			LoadAssemblies (40ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (93ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (22ms)
			SetupLoadedEditorAssemblies (362ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (269ms)
				BeforeProcessingInitializeOnLoad (10ms)
				ProcessInitializeOnLoadAttributes (60ms)
				ProcessInitializeOnLoadMethodAttributes (19ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.008791 seconds.
Begin MonoManager ReloadAssembly
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on CommandBase
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on CommandMultipleBase
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on Reporter
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DealCommand
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Mono: successfully reloaded assembly
- Completed reload, in  1.722 seconds
Domain Reload Profiling:
	ReloadAssembly (1723ms)
		BeginReloadAssembly (102ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (13ms)
		EndReloadAssembly (1573ms)
			LoadAssemblies (79ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (253ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1106ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (5ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (999ms)
				ProcessInitializeOnLoadMethodAttributes (6ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Refreshing native plugins compatible for Editor in 20.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3814 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 267.0 MB.
System memory in use after: 267.1 MB.

Unloading 188 unused Assets to reduce memory usage. Loaded Objects now: 4170.
Total: 3.710300 ms (FindLiveObjects: 0.316300 ms CreateObjectMapping: 0.101700 ms MarkObjects: 2.955900 ms  DeleteObjects: 0.334900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/WGM/BenChiBaoMa/Prefab/Audio
  artifactKey: Guid(73d9dcd3229be5840b518cfd41d4f294) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WGM/BenChiBaoMa/Prefab/Audio using Guid(73d9dcd3229be5840b518cfd41d4f294) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'da77e6ad6523b010fa68e1ece4ebc98b') in 0.019932 seconds 
