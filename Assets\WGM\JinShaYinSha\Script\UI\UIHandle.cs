using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using PathologicalGames;
using DG.Tweening;

namespace WGM.Bet.JinShaYinSha
{
    public class UIHandle : Spawn
    {

        protected ViewManager mViewManager;
        protected ToolsCameraMain mCameraMain;
        protected ToolsCameraNGUI mCameraNGUI;
        protected AudioHandle mAudioHandle;
        protected ParticleHandle mParticleHandle;
        protected Player mPlayer;
        protected Transform mTips;
        protected Transform mSettle;
        protected Transform[] mSettleWinItemNormal = new Transform[AlgDef.ITEM_MAX];
        protected Transform[] mSettleWinItemLight = new Transform[AlgDef.ITEM_MAX];
        protected Transform[] mSettleWinItemBonus = new Transform[AlgDef.ITEM_MAX];
        protected UILabel mScore;
        protected Transform[] mRecord = new Transform[AlgDef.HISTORY_MAX];
        protected UILabel mTimeDown;
        //兔子 猴子 熊猫 老鹰 狮子 孔雀 鸽子 燕子
        protected UILabel[] mMultiple = new UILabel[AlgDef.ITEM_MAX];
        protected UILabel[] mBet = new UILabel[AlgDef.ITEM_MAX];
        protected UILabel[] mTotalBet = new UILabel[AlgDef.ITEM_MAX];
        protected Transform[] mBetMask = new Transform[AlgDef.ITEM_MAX];
        protected Transform[] mJetton = new Transform[AlgDef.ITEM_MAX];
        protected UIMarquee mMarquee;
        protected Transform mTipBet;

        public Player player
        {
            get { return mPlayer; }
        }
        
        //private int mJettonIndex = 0;



        void Start()
        {
            InitUI();
            mViewManager = transform.root.GetComponent<ViewManager>();
            mCameraMain = transform.root.Find("Main Camera").GetComponent<ToolsCameraMain>();
            mCameraNGUI = transform.parent.Find("Camera").GetComponent<ToolsCameraNGUI>();
            mAudioHandle = transform.root.Find("AudioHandle").GetComponent<AudioHandle>();
            mParticleHandle = transform.root.Find("ParticleHandle").GetComponent<ParticleHandle>();
            mPlayer = transform.root.Find("Player").Find(transform.name).GetComponent<Player>();
            mScore = transform.Find("Score").GetComponent<UILabel>();
            mTips = transform.Find("Tips");
            mSettle = transform.Find("Settle");
            for (int i = 0; i < AlgDef.HISTORY_MAX; i++)
            {
                mRecord[i] = transform.Find("Record/" + i.ToString("D2"));
                mRecord[i].GetComponent<UISprite>().spriteName = "底座框";
                mRecord[i].gameObject.SetActive(false);
            }
            mTimeDown = transform.Find("TimeDown/Label").GetComponent<UILabel>();
            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                mMultiple[i] = transform.Find("Multiple/" + i).GetComponent<UILabel>();
                mBet[i] = transform.Find("Bet/" + i).GetComponent<UILabel>();
                mTotalBet[i] = transform.Find("TotalBet/" + i).GetComponent<UILabel>();
                mBetMask[i] = transform.Find("BetMask/" + i);
                mSettleWinItemNormal[i] = mSettle.Find("WinItemNormal/" + i);
                mSettleWinItemLight[i] = mSettle.Find("WinItemLight/" + i);
                mSettleWinItemBonus[i] = mSettle.Find("WinItemBonus/" + i);
            }


             


            for (int i = 0; i < 5; i++)
            {
                mJetton[i] = transform.Find("Jetton/" + i);
                mJetton[i].GetComponent<UIToggle>().onChange.Add(new EventDelegate(OnJettonChange));
            }
            mMarquee = transform.Find("Marquee").GetComponent<UIMarquee>();
            mTipBet = transform.Find("Tips/Bet");

            LibAlg.onBetPrepare += OnBetPrepare;
            LibAlg.onBetCountDown += OnBetConutDown;
            LibAlg.onBetBet += OnBet;
            LibAlg.onBetCancel += OnBet;
            LibAlg.onBetContinue += OnBet;
            LibAlg.onBetResult += OnBetResult;
            LibAlg.onHistoryCardUpdate += OnHistoryCardUpdate;

            Init();

            //请求 牌局列表20个
            LibAlg.SendBetHistroy();
        }

        private void OnDestroy()
        {
            LibAlg.onBetPrepare -= OnBetPrepare;
            LibAlg.onBetCountDown -= OnBetConutDown;
            LibAlg.onBetBet -= OnBet;
            LibAlg.onBetCancel -= OnBet;
            LibAlg.onBetContinue -= OnBet;
            LibAlg.onBetResult -= OnBetResult;
            LibAlg.onHistoryCardUpdate -= OnHistoryCardUpdate;
        }



        private void OnHistoryCardUpdate()
        {
            UpdateHistroyCount();
        }

        protected virtual void Update()
        {
            mScore.text = GlobalCommon.GetMoney(mPlayer.score);

            if (Input.GetKeyDown(KeyCode.B))
            {
                //mMarquee.Run(20 + 28 * 7, 6.0f);
            }
        }

        void InitUI()
        {
            pool = CreatePool(transform.name + "UI");
            pool.matchPoolScale = true;
            pool.transform.localScale = Vector3.one;
        }

        protected virtual void Init()
        {
            LibAlg.Init();
            mSettle.gameObject.SetActive(false);
            mSettleWinItemNormal[0].parent.gameObject.SetActive(false);
            mSettleWinItemBonus[0].parent.gameObject.SetActive(false);
            mSettleWinItemLight[0].parent.gameObject.SetActive(false);

            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                //mMultiple[i].text = LibAlg.player[LibAlg.run.seat].multiples[i].ToString();
                mBet[i].text = "0";
                mTotalBet[i].text = "";
                mBetMask[i].gameObject.SetActive(false);
                mSettleWinItemNormal[i].gameObject.SetActive(false);
                mSettleWinItemLight[i].gameObject.SetActive(false);
                mSettleWinItemBonus[i].gameObject.SetActive(false);
            }

         

            UpdateHistroyCount();

        }

        public void UpdateHistroyCount()
        {
            string type = "";
            string label = "";
            for (int i = 0; i < AlgDef.HISTORY_MAX; i++)
            {
                Debug.Log("shuaxin historyRecords" + i + ":" + LibAlg.player[LibAlg.run.seat].historyRecords[i]);

                if (i >= AlgDef.HISTORY_ShowMAX)
                {
                    mRecord[i].gameObject.SetActive(false);

                    break;
                }

                

                if (LibAlg.player[LibAlg.run.seat].historyRecords[i] < AlgDef.ITEM_MAX)
                {
                    type = "Record" + LibAlg.player[LibAlg.run.seat].historyRecords[i];
                    label = "";
                }
                else if (LibAlg.player[LibAlg.run.seat].historyRecords[i] < 60)
                {
                    type = "Record9";
                    label = "X" + (LibAlg.player[LibAlg.run.seat].historyRecords[i] - 50);
                }
                else if (LibAlg.player[LibAlg.run.seat].historyRecords[i] <= 200)
                {
                    type = "Record8";
                    label = "+" + (LibAlg.player[LibAlg.run.seat].historyRecords[i] - 100);
                }
                else
                {
                    mRecord[i].gameObject.SetActive(false);
                    continue;
                }


                mRecord[i].gameObject.SetActive(true);
                mRecord[i].GetComponent<UISprite>().spriteName = type;
                mRecord[i].Find("Label").GetComponent<UILabel>().text = label;

            }
        }

        void OnBetPrepare()
        {
            LibAlg.SendBetPrepare();
            mAudioHandle.PlayRoundStart();
            Init();
        }


        protected virtual void OnBetConutDown()
        {
            //显示 最大压分
            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                mTotalBet[i].text = "";
            }

     
            mTimeDown.text = LibAlg.player[LibAlg.run.seat].normalTime.ToString();
            mAudioHandle.PlayTimeDown(LibAlg.player[LibAlg.run.seat].normalTime);
            if (LibAlg.player[LibAlg.run.seat].normalTime == 0) mAudioHandle.PlayBetStop();
        }

        void OnBetResult()
        { 
            StartCoroutine(CBetResult());
        }

        protected virtual IEnumerator CBetResult()
        {
            // 每个动物的完整位置映射（处理被通杀通赔分开的情况）
            int[][] animalPositions = new int[][] {
                new int[] {21, 22, 24}, // 索引0: 兔子 (被通赔23分开)
                new int[] {1, 2, 3},    // 索引1: 猴子
                new int[] {25, 26, 27}, // 索引2: 熊猫
                new int[] {18, 19, 20}, // 索引3: 老鹰
                new int[] {4, 5, 6},    // 索引4: 狮子
                new int[] {11, 12, 13}, // 索引5: 孔雀
                new int[] {15, 16, 17}, // 索引6: 鸽子
                new int[] {7, 8, 10},    // 索引7: 燕子 (被通杀9分开)


                new int[] {14,14,14},    // 索引8: 银鲨
                new int[] {0,0,0},    // 索引9: 金鲨
                new int[] {9,9,9},    // 索引10: 通杀
                new int[] {23,23,23},    // 索引10: 通赔
            };

            // int[] seqLight = new int[] { 0  };
            // int[] seqBonus = new int[] { 14 };

            // //通杀通赔 不会进入
            // int[] seqJiade = new int[] {9,23 };





            Tweener tweener;
            int index, order;
            int roundAdd = 7 * 28;
            //WinType winType = (WinType)(LibAlg.player[LibAlg.run.seat].winType & 0x0f);
            //只是用普通中奖
            WinType winType = WinType.Normal;
            print("wintype = " + LibAlg.player[LibAlg.run.seat].winType + " winNum = " + LibAlg.player[LibAlg.run.seat].winNum + " winItem[0] = " + LibAlg.player[LibAlg.run.seat].winItem[0]);

            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                mBet[i].text = GlobalCommon.GetMoney(LibAlg.player[LibAlg.run.seat].bet[i]);
                mTotalBet[i].text = GlobalCommon.GetMoney(LibAlg.player[LibAlg.run.seat].totalBet[i]);
            }

            Transform bonusAudio = null;
            // Transform bonusParticle = null;
            mMarquee.Init();
            // if (winType == WinType.Light)
            // {
            //     order = (LibAlg.player[LibAlg.run.seat].winType >> 4) & 0x0f;
            //     tweener = mMarquee.Run(seqLight[order] + roundAdd, 8.0f);
            //     yield return tweener.WaitForCompletion();
            //     bonusParticle = mParticleHandle.PlayFirework();
            //     yield return new WaitForSeconds(0.5f);
            //     bonusAudio = mAudioHandle.PlayBonus();
            //     yield return new WaitForSeconds(4.5f);
            //     mParticleHandle.Stop(bonusParticle);
            // }
            // else if (winType == WinType.Bonus)
            // {
            //     order = (LibAlg.player[LibAlg.run.seat].winType >> 4) & 0x0f;
            //     tweener = mMarquee.Run(seqBonus[order] + roundAdd, 8.0f);
            //     yield return tweener.WaitForCompletion();
            //     bonusParticle = mParticleHandle.PlayFirework();
            //     yield return new WaitForSeconds(0.5f);
            //     bonusAudio = mAudioHandle.PlayBonus();
            //     yield return new WaitForSeconds(4.5f);
            //     mParticleHandle.Stop(bonusParticle);
            // }


            for (int i = 0; i < LibAlg.player[LibAlg.run.seat].winNum; i++)
            {
                int winItem = LibAlg.player[LibAlg.run.seat].winItem[i];
                index = (winItem >> 0) & 0x0f;
                order = (winItem >> 4) & 0x0f;

                // 二进制调试显示
                print($"=== 中奖项目 {i} 调试信息 ===");
                print($"原始winItem: {winItem} (0x{winItem:X4}) (二进制: {System.Convert.ToString(winItem, 2).PadLeft(16, '0')})");
                print($"index = (winItem >> 0) & 0x0f = {index} (动物索引)");
                print($"  计算过程: {winItem} >> 0 = {winItem >> 0} (0x{(winItem >> 0):X4})");
                print($"  计算过程: {winItem >> 0} & 0x0f = {index} (取低4位)");
                print($"order = (winItem >> 4) & 0x0f = {order} (位置偏移)");
                print($"  计算过程: {winItem} >> 4 = {winItem >> 4} (0x{(winItem >> 4):X4})");
                print($"  计算过程: {winItem >> 4} & 0x0f = {order} (取低4位)");

                // 使用新的位置映射数组
                int tmp = animalPositions[index][order];
                print($"转盘位置: animalPositions[{index}][{order}] = {tmp}");

                //if (order != 0 && (tmp == seqLight[0] || tmp == seqLight[1] || tmp == seqBonus[0] || tmp == seqBonus[1]))
                // if (order != 0 && (tmp == seqLight[0] ||  tmp == seqBonus[0] ))
                // {
                //     tmp += 1;
                // }

                // if (tmp == seqJiade[0] || tmp == seqJiade[1])
                // {
                //     tmp += 1;
                // }

                tweener = mMarquee.Run(tmp + roundAdd, 8.0f);
                yield return tweener.WaitForCompletion();
                yield return new WaitForSeconds(0.3f);
                mBetMask[index].gameObject.SetActive(true);
                yield return new WaitForSeconds(1.0f);
                mAudioHandle.PlayWinItem(index);
                yield return new WaitForSeconds(1.0f);
            }


            mAudioHandle.Stop(bonusAudio);
            mSettle.gameObject.SetActive(true);
            mSettle.Find("Win/Label").GetComponent<UILabel>().text = GlobalCommon.GetMoney(LibAlg.player[LibAlg.run.seat].win);

            // if (winType == WinType.Light)
            // {
            //     mSettle.Find("WinItemLight").gameObject.SetActive(true);
            //     for (int i = 0; i < LibAlg.player[LibAlg.run.seat].winNum; i++)
            //     {
            //         index = ((LibAlg.player[LibAlg.run.seat].winItem[i] >> 0) & 0x0f);
            //         mSettleWinItemLight[index].gameObject.SetActive(true);
            //         mSettleWinItemLight[index].GetComponent<Animator>().Play("show");
            //         mSettleWinItemLight[index].Find("Label").GetComponent<UILabel>().text = "X" + LibAlg.player[LibAlg.run.seat].multiples[index];
            //     }
            // }
            // else if (winType == WinType.Bonus)
            // {
            //     mSettle.Find("WinItemBonus").gameObject.SetActive(true);
            //     mSettle.Find("WinItemBonus/Bonus").GetComponent<Animator>().Play("show");
            //     mSettle.Find("WinItemBonus/Bonus/Label").GetComponent<UILabel>().text = "+" + LibAlg.player[LibAlg.run.seat].winJp;
            //     index = ((LibAlg.player[LibAlg.run.seat].winItem[0] >> 0) & 0x0f);
            //     mSettleWinItemBonus[index].gameObject.SetActive(true);
            //     mSettleWinItemBonus[index].GetComponent<Animator>().Play("show");
            //     mSettleWinItemBonus[index].Find("Label").GetComponent<UILabel>().text = "X" + LibAlg.player[LibAlg.run.seat].multiples[index];
            // }
            // else
            {
                mSettle.Find("WinItemNormal").gameObject.SetActive(true);
                index = ((LibAlg.player[LibAlg.run.seat].winItem[0] >> 0) & 0x0f);
                mSettleWinItemNormal[index].gameObject.SetActive(true);
                mSettleWinItemNormal[index].GetComponent<Animator>().Play("show");
                mSettleWinItemNormal[index].Find("Label").GetComponent<UILabel>().text = "X" + LibAlg.player[LibAlg.run.seat].multiples[index];
            }
            yield return new WaitForSeconds(4f);

            LibAlg.SendBetEnd();
        }

        protected virtual void OnBet()
        {
            if (!LibAlg.player[LibAlg.run.seat].success)
            {
                PlayBetFailed();
                return;
            }
            mAudioHandle.PlayBet();
            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                mBet[i].text = GlobalCommon.GetMoney(LibAlg.player[LibAlg.run.seat].bet[i]);

                //mTotalBet[i].text = GlobalCommon.GetMoney(LibAlg.player[LibAlg.run.seat].totalBet[i]);
                mTotalBet[i].text = "";
            }

        
        }

        void OnJettonChange()
        {
            UIToggle t = UIToggle.GetActiveToggle(1);
            if (t == null) return;
            //mJettonIndex = int.Parse(t.transform.name); 
            var label = t.GetComponentInChildren<UILabel>();
           
            mPlayer.bet = Convert.ToInt32(label.text);
        }

        public void ShowHideBetPanel(bool show)
        {
            transform.root.Find("Background/BetPanel").gameObject.SetActive(show);
            transform.Find("Multiple").GetComponent<UIWidget>().alpha = show ? 1 : 0;
            transform.Find("Bet").GetComponent<UIWidget>().alpha = show ? 1 : 0;
            transform.Find("TotalBet").GetComponent<UIWidget>().alpha = show ? 1 : 0;
            transform.Find("BetMask").GetComponent<UIWidget>().alpha = show ? 1 : 0;
            transform.Find("Jetton").GetComponent<UIWidget>().alpha = show ? 1 : 0;
            ETCInput.GetControl("Cancel").gameObject.SetActive(show);
            ETCInput.GetControl("Continue").gameObject.SetActive(show);
            ETCInput.GetControl("Auto").gameObject.SetActive(show);
            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                ETCInput.GetControl("Bet" + i).gameObject.SetActive(show);
            }
        }

        public void ShowHideRecordPanel(bool show)
        {
            transform.Find("Record").gameObject.SetActive(show);
        }

        public void PlayNotBetTime()
        {
            mTipBet.GetComponent<UILabel>().text = "非押注时间";
            mTipBet.GetComponent<UIPlayTween>().Play(true);
        }

        public void PlayBetStart()
        {
            mTipBet.GetComponent<UILabel>().text = "开始押注";
            mTipBet.GetComponent<UIPlayTween>().Play(true);
        }

        public void PlayBetFailed()
        {
            mTipBet.GetComponent<UILabel>().text = "限红超限，无法操作";
            mTipBet.GetComponent<UIPlayTween>().Play(true);
        }

        public void PlayBankerBetForbidden()
        {
            mTipBet.GetComponent<UILabel>().text = "庄家禁止下注";
            mTipBet.GetComponent<UIPlayTween>().Play(true);
        }

        public virtual void PlayOverBetMax(long score)
        {
            mTipBet.GetComponent<UILabel>().text = "押分超限，该门最大押分为" + GlobalCommon.GetMoney(score);
            mTipBet.GetComponent<UIPlayTween>().Play(true);
        }


        public bool GetActive(Transform instance)
        {
            if (instance == null)
            {
                return false;
            }
            return pool.IsSpawned(instance);
        }

        public IEnumerator WaitForInactive(Transform instance)
        {
            while (GetActive(instance))
            {
                yield return null;
            }
        }

        public void Stop(Transform instance)
        {
            if (pool.IsSpawned(instance))
            {
                pool.Despawn(instance);
            }
        }
    }
}