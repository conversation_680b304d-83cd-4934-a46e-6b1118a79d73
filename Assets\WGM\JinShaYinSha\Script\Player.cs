using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using PathologicalGames;
using DG.Tweening;

namespace WGM.Bet.JinShaYinSha
{
    public enum PlayerStatus
    {
        Idle = 0,
        Exploded = 1 << 3,
        AutoFire = 1 << 4,
    }

    public class Player : Spawn
    {
        public Sprite spriteAuto;
        public long score { get { return LibAlg.player[LibAlg.run.seat].score; } }

        //private int[] mBetScore = new int[] { 1, 100, 1000, 10000 };
        //private int mBetIdx = 1;
        //public int bet { get { return mBetScore[mBetIdx]; } set { mBetIdx = value; } }

        public int bet { get; set; }

        public int win
        {
            get
            {
                int sum = LibAlg.player[LibAlg.run.seat].winJp;
                for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                {
                    sum += LibAlg.player[LibAlg.run.seat].winScore[i];
                }
                return sum;
            }
        }

        public int betSum
        {
            get
            {
                int sum = 0;
                for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                {
                    sum += LibAlg.player[LibAlg.run.seat].bet[i];
                }
                return sum;
            }
        }

        private ViewManager mViewManager;
        public ViewManager viewManager { get { return mViewManager; } }

        private UIHandle mUIHandle;
        public UIHandle ui { get { return mUIHandle; } }

        PlayerStatus mStatus;

        private AudioSource mAudioSource;
        private AudioHandle mAudioHandle;

        bool mAutoContinue;

        public bool prizeGiving
        {
            get
            {
                return false;
            }
        }



        void Start()
        {
            mViewManager = transform.root.GetComponent<ViewManager>();
            mUIHandle = viewManager.uiHandle;
            mAudioSource = GetComponent<AudioSource>();
            mAudioHandle = transform.root.Find("AudioHandle").GetComponent<AudioHandle>();

            LibAlg.onBetCountDown += OnBetCountDown;
        }

        void Update()
        {
            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                AppKeyCode code = (AppKeyCode)((int)AppKeyCode.Bet0 << i);
                if (DealCommand.GetKeyDown(code) ||
                    DealCommand.GetKeyLongMulti(code))
                {
                    if (LibAlg.player[LibAlg.run.seat].isBanker)
                    {
                        ui.PlayBankerBetForbidden();
                        break;
                    }

                    if (LibAlg.player[LibAlg.run.seat].normalTime > 0)
                    {
                        if (HasEnoughScoreForBet(i)) LibAlg.SendBetBet(i, bet);
                    }
                    else
                    {
                        ui.PlayNotBetTime();
                        break;
                    }

                    if (BetOverLimit(i) > 0)
                    {
                        ui.PlayOverBetMax(BetOverLimit(i));
                        continue;
                    }

                    if (!HasEnoughScoreForBet(i))
                    {
                        viewManager.uiGlobal.EventUISettingGetScore();
                    }
                }
            }

             

            if (DealCommand.GetKeyDown(AppKeyCode.Cancel))
            {
                if (betSum > 0) LibAlg.SendBetCancel();
            }

            if (DealCommand.GetKeyDown(AppKeyCode.Continue))
            {
                if (betSum == 0)
                    LibAlg.SendBetContinue();
            }

            if (DealCommand.GetKeyStay(AppKeyCode.Auto, spriteAuto))
            {
                if (mAutoContinue)
                {
                    if (betSum == 0)
                    {
                        mAutoContinue = false;
                        LibAlg.SendBetContinue();
                    }
                }
            }
        }

        void OnBetCountDown()
        {
            if (LibAlg.player[LibAlg.run.seat].normalTime == LibAlg.run.bet_time)
            {
                mAutoContinue = true;
            }
        }

        int BetOverLimit(int index)
        {
            //return LibAlg.player[LibAlg.run.seat].bet[index] + bet > (int)LibAlg.run.bet_max 
            //    ? (int)LibAlg.run.bet_max 
            //    : 0;

            var currMax = LibAlg.run.room_betMax[LibAlg.run.room_select];

            return LibAlg.player[LibAlg.run.seat].bet[index] + bet > currMax
              ? currMax
              : 0;
        }

        bool HasEnoughScoreForBet(int index)
        {
            return score > 0 && score + LibAlg.player[LibAlg.run.seat].bet[index] >= LibAlg.run.bet_min;
        }

        #region Status
        public PlayerStatus SetStatus(PlayerStatus status, bool enable)
        {
            return mStatus = enable ? mStatus | status : mStatus & ~status;
        }

        public bool GetStatus(PlayerStatus status)
        {
            return (mStatus & status) != 0 ? true : false;
        }
        #endregion

        #region Audio
        public void AudioPlay(AudioClip clip, bool loop = false)
        {
            if (mAudioHandle.muteSE) return;
            mAudioSource.clip = clip;
            mAudioSource.Stop();
            mAudioSource.loop = loop;
            mAudioSource.Play();
        }

        public void AudioStop()
        {
            mAudioSource.Stop();
        }
        #endregion
    }
}