﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>8.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_2020_3_48;UNITY_2020_3;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;BSYX;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>2020.3.48f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Client\Manager\SceneLoadManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenScale.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIModifyPass.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Misc\PlayerLocation.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\ParticleHandle.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_ChipManager.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\UI\UIHallTable.cs" />
    <Compile Include="Assets\Client\Utility\DebugTestDC.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\UtilsTool.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\System\InvDatabase.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Login\UILoginHallControl.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIWinLight.cs" />
    <Compile Include="Assets\Client\Utility\LuaHelper.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIStandingScoreItem.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\BG\BGBase.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_GameUtility.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenProShortcuts.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIAnchor.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIGoldRoom.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UIBigWinTip.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_4Gradients.cs" />
    <Compile Include="Assets\WGM\Common\Script\Load\LoadScene.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Roll\RollPanel.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenTextMeshPro.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\DuihuanJLMB.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\VersionManager\VersionManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\JsonReader.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\Player.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UEHappyTime.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_NotificationCenter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ExtensionVector.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\ViewManager.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\HallVersion.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\UIGameWindow.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaDLL.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\LagRotation.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\AudioHandle.cs" />
    <Compile Include="Assets\Client\Utility\DataGrid\DataGrid.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_ShowFps.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_MessagePrompt.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Outline.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\HNMessageObservers.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMJitterData.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UI\MiniGame.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\_CameraShake.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\LoadAsset\AssetLoadingSync.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Mesh Generation\SpineMesh.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIScrollView.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UEGainScore.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\CodeTable.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIToggle.cs" />
    <Compile Include="Assets\7zip\Compress\LZMA\LzmaBase.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIDropdownExt.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\Game\UIStorageSlot.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_UICameraAdjustor.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\BG\BGCrab.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_ResourcesManager.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Animation.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_UICameraAdjustor.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\RoomItem\UIMatchDetail.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UINewRankItem.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_HistoryRecordManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\UI\UIAdapterGap.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_DataCenter.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\Lzma.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\ATT\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\DaTingG178\OpenWeb.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\DownRes\HNHttpDwon.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UIRobPanel.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\Player.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UISavedOption.cs" />
    <Compile Include="Assets\Client\Utility\UniClipboard.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Distortion_Additive.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaMethodCache.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ButtonSelectActive.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIDateSelecter.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Fire.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\EventDelegate.cs" />
    <Compile Include="Assets\WGM\Common\Script\Auto\AutoRotate.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\HallLeftMenu.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Posterize.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\Lexer.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIScrollBar.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\CreateRoom\UIJoinRoom.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\Player.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\FPSExample\FPSPlayerControl.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_TrackedReferenceWrap.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_DestroyedFX.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\DemoPlayer.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\NetworkClient\BMW_NetworkClient.cs" />
    <Compile Include="Assets\Client\Framework\Core\View.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_FireAdditive.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCSingleton.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_ScreenAdapt.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\HallIcon\IconButtonItem.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UITextList.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Components\SkeletonMecanim.cs" />
    <Compile Include="Assets\7zip\Common\InBuffer.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\Fish.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\RegionlessAttachmentLoader.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Poker\Poker.cs" />
    <Compile Include="Assets\WGM\ATT\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\AnimationReferenceAsset.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\Player.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\HallExchange\HistoryExchangePanel.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_Filcker.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\RoomItem\HNMatchRoom.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIRoomItem.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UE\UERoyalFlush.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\Bezier.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\PinchMe.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Info\BtnPanel.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\Game\UICursor.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNGameResLoader.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UISoundVolume.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMColorData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\PathMgr.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_WoodFX.cs" />
    <Compile Include="Assets\Client\Utility\ModelRotateContrl.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UICustomer.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_RotateSelf.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\AdvancedExamples\MultiCamera\MultiCameraUI.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\Player.cs" />
    <Compile Include="Assets\CielaSpike\Thread Ninja\ThreadNinjaMonoBehaviourExtensions.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\UniGifTest.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNAssetEvent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIStanding.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\ClubControl.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishCombineSameType.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\OldStruct\NET_ROOM_LOGON_RESULT.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\ParticleHandle.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\Marquee.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateClip.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIPlayTween.cs" />
    <Compile Include="Assets\Client\Utility\AnimationCallLua.cs" />
    <Compile Include="Assets\VisionTimer\Examples\Scheduling\TestComponent.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\SpringPosition.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\ExampleDragDropSurface.cs" />
    <Compile Include="Assets\Client\Utility\ParticleSystemController.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\UIEventListener.cs" />
    <Compile Include="Assets\WGM\Common\Script\Tween\TweenUV.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\EmailManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Spread\SpreaderInfo.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_Collections_Generic_DictionaryWrap.cs" />
    <Compile Include="Assets\Client\ToLua\Source\LuaConst.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDragObject.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\PlayIdleAnimations.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UINewMainRankItem.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\OneFinger\DragMe.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\VoiceChatPanel.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_BetItem.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButtonRotation.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\Client\ToLua\Reflection\LuaConstructor.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\ByteReader.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_BehaviourWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MyStruct\CommercerInfo.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Components\SkeletonAnimation.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIStretch.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\UICom.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UITrackOrderItem.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMSimpleButton.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishAnimator.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\DemoPlayer.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_PatternAdditive.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\HNSocialManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Room\RoomSocketBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMAppendExample.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Pixel8bitsBW.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\Robot\JiqirenDBXJTable.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\UE\UEBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\LoadAsset\AssetMultiLoading.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\ViewManager.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_TimeManager.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Poker\PokerSprite.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\JoinRoomMgr.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\Control\LogoSwitch.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Common\GameConfig.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\ChongzhiMB.cs" />
    <Compile Include="Assets\Client\Utility\DragBubble.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\UI\ShowTips.cs" />
    <Compile Include="Assets\Reporter\Reporter.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_Dice.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\Dispatch\DispatchCallback.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Negative.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\FSM\State.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UEBoss.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Spine\SpineMgr.cs" />
    <Compile Include="Assets\WGM\Common\Script\Auto\AutoInactive.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Robot\JiqirenATT3Table.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\Global.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UGUITools.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_SandFX.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_UIHomepagePanel.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_AudioSourceWrap.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_TypeWrap.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonRenderSeparator\SkeletonPartsRenderer.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_HistoryRecordManager.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\ParticleHandle.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateBehaviour.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\BG\BGBase.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\SkeletonData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\HNFetchPackage.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Room\SelfRoom\RoomProtocolID.cs" />
    <Compile Include="Assets\MatiarlsForTestDAjinAnZhuangBao\TestCaizhi.cs" />
    <Compile Include="Assets\WGM\ABackup\BenChiBaoMa\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\BG\BGBase.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishCrabBomb.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\LookAtTarget.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Common\HNSokcetType.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\TwoFingerGesture.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\SameColor.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\BG\BGBase.cs" />
    <Compile Include="Assets\Client\Network\Protocal.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishNormal.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISysNotice.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEExplodeBonus.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\AdvancedExamples\AutoSelect\MultiLayerTouch.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_JellyAutoMove.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenFOV.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\Handlers.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\NGUIAtlas.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Frozen.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UEBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\UserInfoModule.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\ParserToken.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UICenterOnClick.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEFourOfAKindB.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\Button-Event-Input\ButtonInputUI.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MatchInfoModule.cs" />
    <Compile Include="Assets\Client\Network\Net\SwapList.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\LagPosition.cs" />
    <Compile Include="Assets\Client\Utility\EventHandler.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Record\RecordPlayback.cs" />
    <Compile Include="Assets\Client\Framework\AppFacade.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Player.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_GameObjectWrap.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityKinematicShadow.cs" />
    <Compile Include="Assets\Client\Utility\EnhanceScrollView\UDragEnhanceView.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\CP936.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\Fish.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Common\GameQuitHelp.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_RotateAround.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Info\TwinklePanel.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\Button-Event-Input\ButtonUIEvent.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDragDropContainer.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\MobileUtil.cs" />
    <Compile Include="Assets\WGM\NBA\Script\ViewManager.cs" />
    <Compile Include="Assets\Client\Utility\TextureChange.cs" />
    <Compile Include="Assets\Client\Network\Net\SocketError.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\Player.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\Player.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMVertexMod.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\RecordTool.cs" />
    <Compile Include="Assets\WGM\NBA\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UE\UEBigPrize.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_TimeWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UICopyText.cs" />
    <Compile Include="Assets\Client\ToLua\Reflection\LuaProperty.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\GameListConfig.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNEnctryptTools.cs" />
    <Compile Include="Assets\Client\Utility\InfiniteList\GridItem.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\EventData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\ScriptsForAssetBundleSystem\AssetBundleLoadOperation.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_ControlCenter.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIBetPanel.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEBigPrize.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\Poker\Poker.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\TransformConstraintData.cs" />
    <Compile Include="Assets\Client\Utility\Util.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Script\UniWebViewMessage.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\new\UIBetPanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\HNMessageCache.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\SpineAtlasAsset.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Animation\UI2DSpriteAnimationPro.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCButton.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\BoneFollowerGraphic.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\VoiceChatSetting.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_ResultManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\HNPropertiesDesc.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\RegionAttachment.cs" />
    <Compile Include="Assets\Client\Utility\CollisionHelper.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Http\NetManager.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TwistMe.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletNet.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\UE\UEBase.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\UE\UEBase.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\MultiFinger\FingerTouch.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNSingleton.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\UpdateGold.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UIMainPanel.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Pixel8bitsGB.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\BG\BGBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\Interface\ISocket.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\DownRes\DownloadErrorCode.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Components\SkeletonRenderer.cs" />
    <Compile Include="Assets\WGM\NBA\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\UI\UIItem.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\Poker\Poker.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\AudioHandle.cs" />
    <Compile Include="Assets\Client\ToLua\Misc\LuaResLoader.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\BetterList.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\GameResource.cs" />
    <Compile Include="Assets\Client\Framework\Core\Message.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\AttachmentLoader.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_HistoryRecordManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UILocalize.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNMatchUI.cs" />
    <Compile Include="Assets\CielaSpike\Thread Ninja\Task.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\EnumLabel.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TwoSwipe.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishFlounder.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_RotateSelf.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Timer\IUpdate.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\ParticleEnable.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UIDepth.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_BurningFX.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\UIBottom.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNAudioPlay.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\PropWindow\SwitchAccountWindow.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\IphonePanel.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\LoadLevelScript.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNUserActionEvent.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\BG\BGBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\AssetInfo.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Roll\RollItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\Window.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_Chip.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_UIManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\StrUtil.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_BlackMask.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\BMGlyph.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\Player.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\ThreadPoolScheduler.cs" />
    <Compile Include="Assets\WGM\Common\Script\Network\CommandBase.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\SkeletonGraphicMirror.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Color.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\AllJP.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\OldStruct\ComKindInfo.cs" />
    <Compile Include="Assets\WGM\NBA\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TwoDoubleTapMe.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Cannon\CannonDrill.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_ResultData.cs" />
    <Compile Include="Assets\7zip\Common\CommandLineParser.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNKeyDictionary.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_ChangeLabelValue.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ComponentExtensions.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCDPad.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIRecordPlayback.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\PerJP.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\Tween\TweenPositionAdd.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNSimpleTask.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatSocketTemplate.cs" />
    <Compile Include="Assets\WGM\Common\Script\Watching.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\TextVirtical.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Transitional\TransitionalSence.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\MGridItemOffset.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\OldStruct\UserInfoStruct.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEFiveBars.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubRoomList.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\AnimatedWidget.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenFill.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenPosition.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\Code.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\LanguageSelection.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\Poker\Poker.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIGameList.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UITable.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\ParticleHandle.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishCreater.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\RoomItem\HNRoomPsdInputUI.cs" />
    <Compile Include="Assets\Client\ConstDefine\GameConst.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MyStruct\MatchInfo.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\DontDestroy.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\UI\CameraRect.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_GlobalManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_GameAdapter\GameLogicBase.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_LightWrap.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UISaveTakeScorePanel.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\UI\UIMarquee.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\TouchPad-Event-Input\TouchPadUIEvent.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\UE\UEBase.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\System\InvAttachmentPoint.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_RobResultInfo.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCBase.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\YieldInstructions\WaitForSpineEvent.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Mesh Generation\DoubleBuffered.cs" />
    <Compile Include="Assets\Client\Network\Converter.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\FPSExample\ImpactEffect.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\HNCardPayControl.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenWidth.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Cannon\CannonLaser.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Pattern.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\NativeWebView\Test_webview.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\initialize\HNUnityInterface.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\UIGridAuto.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIPanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\MutiPlayerAdapter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\UnityActivityWatchdog.cs" />
    <Compile Include="Assets\Client\Utility\CreatQR.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\Game\UIItemSlot.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Cannon\Cannon.cs" />
    <Compile Include="Assets\Client\Common\LuaBehaviour.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIImageButton.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Tools\OnGUIPro.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_Result.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_RobZhuangManager.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\PathConstraint.cs" />
    <Compile Include="Assets\Client\Utility\EnhanceScrollView\DragEnhanceView.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_UISettingPanel.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaValueType.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Sdk\NativeManager.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\Game\EquipItems.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIProgressBar.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\FishFont.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UIExplainPanel.cs" />
    <Compile Include="Assets\Client\Utility\EnhanceScrollView\EnhanceScrollViewDragController.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_Result.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\HNPropertiesUtils.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\BG\BGBase.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDragDropItem.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Script\UniWebViewOrientation.cs" />
    <Compile Include="Assets\WGM\Common\Script\Network\DownloadData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\Interface\INameInfo.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_CircleFade.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_RobZhuangData.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\PropertyReference.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\SourceRefItem.cs" />
    <Compile Include="Assets\Client\Utility\ButtonDragHandle.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\MGrid.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UISettings.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModulePhysics2D.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenAnimation.cs" />
    <Compile Include="Assets\WGM\Common\Script\AudioPar\ParticleHandleBase.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\Control\HallUIControl.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\UI\UIMarquee.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\ChangeNGUILayer.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Common\NetProtocol\INetHead.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBanner.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenTk2d.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Cannon\CannonEnergy.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\ViewManager.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\AudioHandle.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\ControlEventInput\ControlUIEvent.cs" />
    <Compile Include="Assets\7zip\Compress\RangeCoder\RangeCoderBitTree.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\CUIButton.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishVirtual.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Common\NetProtocol\HNRC4.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNTaskEvent.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNCallEvents.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_ChangeLabelValue.cs" />
    <Compile Include="Assets\7zip\Common\CRC.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\ParticleCtrl.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMAutoClipData.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\Outline8.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\NetworkClient\DICE_NetworkClient.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Roll\RollTest.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UE\UEPassBonus.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\MultiFinger\MutliFingersScreenTouch.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenOrthoSize.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\UI\GetScore.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\UI\UIMarquee.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\BG\BGBase.cs" />
    <Compile Include="Assets\WGM\Common\Script\Auto\AutoTwinkle.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Bullet\Bullet.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\UICkeckLanguage.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMSampleLink.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNDissolvePanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Login\ErrorCode.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEFiveOfAKind.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\XXTEA.cs" />
    <Compile Include="Assets\Client\Manager\Notification\LocalNotification.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\VoiceTest.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\Bowl.cs" />
    <Compile Include="Assets\Reporter\Test\TestReporter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\VoiceChatRecord.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNAttribute.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Poker\Poker.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\PackageManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\RoomNetWorkMgr.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\UIGameIcon.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\HNArrayByteBuff.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\Common\Script\Camera\ToolsCamera.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_Test2.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\OneFinger\Swipe.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Poker\AutoFollowOperaPoker.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Cannon\CannonHappyTime.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonRenderSeparator\SkeletonRenderSeparator.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\UI\UIDRecord.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_PlasmaRainbow.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UI\UIHallTable178.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Additive.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\ActiveAnimation.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Blur.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Components\BoneFollower.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_ColorChange.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\SuperControl\SuperControll.cs" />
    <Compile Include="Assets\Client\Utility\ShowFPS.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_RunFrame.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\LeftMenuButtonState.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\BG\BGBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\RoomItem\UINewRoom.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Demo\2.UseWithCode\UseWithCodeSceneManager.cs" />
    <Compile Include="Assets\WGM\Common\Script\Camera\ToolsCameraNormal.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_Collections_ObjectModel_ReadOnlyCollectionWrap.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\CommonPath.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_Notification.cs" />
    <Compile Include="Assets\CielaSpike\Thread Ninja\Example\ExampleScript.cs" />
    <Compile Include="Assets\WGM\ATT\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\ATT\Script\ViewManager.cs" />
    <Compile Include="Assets\Client\ToLua\Misc\LuaClient.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Robot\JiqirenNBA.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UIScrollControlHorizontal.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\IMProtocol.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\GameItem\UICheckGame.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishRandom.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNNetToolKit.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\VoiceUtils.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_RunFrameManager.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\CircleOutline.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\SameType.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TwoDragMe.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\DownAsset\AssetDown.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\MathUtils.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\FindShader.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_NotificationCenter.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UIResultPanel.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Misc\PlayerLocation.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\BaseFinger.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\MonoEncoding.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishCrabLaser.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Room\PCRoom\PCRoomSocket.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UISaveTakeScorePanel.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\PlayableHandle Component\SkeletonAnimationPlayableHandle.cs" />
    <Compile Include="Assets\WGM\ATT\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\Lexer.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\ParticleHandle.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\ParticleHandle.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_RotateAround.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_RotateAround.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\FixPlaneAspectRatio.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\UIToggledColor.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_SoundSystem\HNMusicPlayLifeTime.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISetting.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\WaterWaveEffect.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\Finger.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEStrFlush.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Pixel.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishCuttle.cs" />
    <Compile Include="Assets\Client\ToLua\Core\ToLua.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\PlayableHandle Component\SpinePlayableHandleBase.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Roll\MovePanel.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\UniGifTexture.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\LuaInterface_LuaMethodWrap.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UE\UEFourOfAKind.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\AutoFollow.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\UniGifConst.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_PlayerDataManager.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\JsonWriter.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_SkyCloud.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UISettingPanel.cs" />
    <Compile Include="Assets\Client\Utility\ToggleGroupSwitch.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Protocol\GameLogicTable.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\Player.cs" />
    <Compile Include="Assets\Client\Utility\PageScroll.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\LocalizationData.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\UIRect.cs" />
    <Compile Include="Assets\WGM\Robot\JiqirenDating.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_Item.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Cannon\CannonMissile.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ButtonEvent.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_PlayerData.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\NetworkClient\DT_NetworkClient.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Transitional\UIUpdateTip.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\SkeletonUtility\SkeletonUtility.cs" />
    <Compile Include="Assets\Client\Utility\SimpleJson.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletBomb.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\SkeletonExtensions.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\Client\Framework\Interfaces\IMessage.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\MoneyExchange\MnyExLogic.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipMixerBehaviour.cs" />
    <Compile Include="Assets\WGM\Common\Script\Network\Network.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenAlpha.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\ViewManager.cs" />
    <Compile Include="Assets\Client\Utility\DepthParticleSystemController.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_TextureWrap.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UEBombScreen.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MyStruct\HallUserInfo.cs" />
    <Compile Include="Assets\Client\ToLua\Injection\LuaInjectionStation.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\YieldInstructions\WaitForSpineTrackEntryEnd.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ImageAnimation.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Components\PointFollower.cs" />
    <Compile Include="Assets\WGM\ATT\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UISettle.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBossTentacle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\PersonalData.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIWrapContent.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\Game\UIItemStorage.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_ItemManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\AnimationOrTween.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\LoadAsset\AssetLoadFromFile.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNUIChatInput.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityGroundConstraint.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Common\Interface\IDestroy.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UIGoodLuck.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMWaveData.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\ChongzhiJLMB.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Script\UniWebViewEdgeInsets.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_BetItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\DownAsset\DownAndDecompress.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\HNRoomRuleUtis.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Skeleton.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISelectGame.cs" />
    <Compile Include="Assets\Client\Manager\ObjectPoolManager.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\GameItem\GameInfo.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UIHomepagePanel.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\UnityUI\UITwistPinch\UIPinch.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Wave.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HallEvent\HNTipEvent.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\ViewMgr.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_Tags.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UIPanelBase.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\DuihuanMB.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UISlider.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\FaceItemButton.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UIScrollControl.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Common\NetProtocol\ProtocolPackage.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUnityVersion.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Player.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UEBrokenPart.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNTolerantControl.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_GoldenFX.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\JsonReader.cs" />
    <Compile Include="Assets\Client\Utility\LuaDebugTool.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\ParticleHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\ClubProtocol.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMGradientData.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIMarquee.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\Player.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\JsonMapper.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEPassBonus.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\PerBonus.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISingleRecordItem.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBossLantern.cs" />
    <Compile Include="Assets\WGM\Common\Script\Tween\TweenScroll.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIInputOnGUI.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\UE\UEBase.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaEvent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\Interface\IRoomInfo.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletMissile.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_SingletonBase.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButtonScale.cs" />
    <Compile Include="Assets\Client\Utility\InfiniteList\InfiniteList.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\GlobalData.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_GameController.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMSoundClipData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UITrackOrderPanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\ReplaceMatTextureRuntime.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEStrFlush.cs" />
    <Compile Include="Assets\VisionTimer\Examples\TimeBomb\TimeBomb.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUtils.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_GameAdapter\ClientControl.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEMiniGame.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\PanWithMouse.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Info\ScorePanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Spread\spread_layerInfo.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubTalk.cs" />
    <Compile Include="Assets\Client\Utility\Yielders.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIStandingWaterItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMMaterialData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Utility\STMCurveGenerator.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaBeatEvent.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateTrack.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\LoadExamples.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_ResManagerCenter.cs" />
    <Compile Include="Assets\WGM\ATT\Script\Poker\Poker.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UIPlayerPlane.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBossDragon.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Ragdoll\SkeletonRagdoll2D.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\JsonData.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_RobZhuangData.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_SongDengAnimMgr.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\ControlEventInput\ControlUIInput.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\BG\BGBase.cs" />
    <Compile Include="Assets\Client\Utility\ButtonLongPress.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\YieldInstructions\WaitForSpineAnimationComplete.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNClubEvent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\HardwareInfo .cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMContinue.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_ButtonBase.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\EnvelopContent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\PayType.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Lightning.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\LibUnityPlugin.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_HistoryRecordItem.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\SpinWithMouse.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishBombScreen.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Prop\PropManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\CamerAdjust.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MyStruct\UserInfo.cs" />
    <Compile Include="Assets\WGM\Common\Script\Load\Load.cs" />
    <Compile Include="Assets\Client\ToLua\Misc\LuaCoroutine.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMEventInterpreter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\UIManager.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\LuaInterface_LuaOutWrap.cs" />
    <Compile Include="Assets\Client\Android\PayCallback.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISwitchAccount.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\EventTriggerListener.cs" />
    <Compile Include="Assets\Client\Utility\SingleSequenceFrameAnim.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\UI\UIItem.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_ApplicationWrap.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\BoundingBoxAttachment.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Common\UIQuitGame.cs" />
    <Compile Include="Assets\Client\Utility\ControlShader.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UIScatterCountTip.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNBoxUI.cs" />
    <Compile Include="Assets\Client\Framework\Interfaces\ICommand.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UIAdaptiveWithScreen.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Script\Helper\UniWebViewHelper.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_Test3.cs" />
    <Compile Include="Assets\Client\Manager\SoundManager.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Slot.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\UIClickScrollBar.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\UIBasicSprite.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\DelegateFactory.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNOnlineEvent.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCArea.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\LocalizationManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIAtlas.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Record\RecordControl.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_MaterialWrap.cs" />
    <Compile Include="Assets\WGM\NBA\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\Client\Framework\Interfaces\IView.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\SkeletonJson.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIForwardEvents.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UECrabLaser.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\OldStruct\MSG_GP_ContestApplyInfo.cs" />
    <Compile Include="Assets\Client\Manager\TPAtlasManager.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_PlayerManager.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MyStruct\NameInfo.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishAnimator.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateMixerBehaviour.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ButtonActive.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\Player.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\HideAllChildren.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Pixel8bitsC64.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_Collections_Generic_Dictionary_ValueCollectionWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIShowBox.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\VersionManager\VersionItem.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\Client\Utility\SetTextureOffset.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_Filcker.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_WaterAndBackgroundDeluxe.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_PlasmaShield.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_MessagePrompt.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UIHistoryRecordPanel.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\UnityUI\UITwistPinch\UITwist.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\SignProtocolcs.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipBehaviour.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\RoomItem\MatchUtils.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ResourceLoder\ResourceLoader.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\EventDelegate.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\VersionManager\GameManager.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_EdgeColor.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Waterfall.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\UIDrawCall.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\UI\LongPressOrClickEventTrigger.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UECrabBomb.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\UpdateCurGameName.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Teleportation.cs" />
    <Compile Include="Assets\Client\Manager\GameManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\IosPlatformUtil.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Hologram2.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Cannon\Cannon.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\DownRes\DownloadThread.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDraggableCamera.cs" />
    <Compile Include="Assets\Client\Utility\DebugUILine.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\HNGamePoolManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\RoomProtocol.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UISettingPanel.cs" />
    <Compile Include="Assets\WGM\Common\Script\Tween\TweenFadeout.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_ChangeLabelValue.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\PayItem.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\AudioHandle.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_WaterAndBackground.cs" />
    <Compile Include="Assets\Client\Main.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\VertexAttachment.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\FindMultiShader.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIRoomManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIGrid.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\UI\UIJetton.cs" />
    <Compile Include="Assets\Client\ToLua\Misc\LuaProfiler.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\RoomInfoModule.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\HeadMgr.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Personal\PersonalCenter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UIOnDisableTween.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\BMFont.cs" />
    <Compile Include="Assets\Client\ConstDefine\ManagerName.cs" />
    <Compile Include="Assets\VisionTimer\Examples\Clock\Clock.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\UITip.cs" />
    <Compile Include="Assets\Client\Network\Net\AbstarctSocket.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishProjection.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Common\UpdateObject.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\AdvancedExamples\MultiCamera\MultiCameraTouch.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletLaser.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\MainThreadDispatcher.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNFlipCard.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletLock.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\LuaInterface_DebuggerWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubCreate.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\QueueGameTips.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_ButtonBase.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\NetworkClient\DT_NetworkHeartBeat.cs" />
    <Compile Include="Assets\Client\Android\DownProject.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Spread\SpreadLogic.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMVoiceData.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_GameController.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Demo\1.Basic\TopSceneManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\SpringPanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ExtensionUGUI.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\ScriptsForAssetBundleSystem\AssetBundleManager.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\LuaInterface_LuaConstructorWrap.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishFormation.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\Scale2dCamera.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_GlobalManager.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\Bezier.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Mystic_Distortion_Additive.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubRename.cs" />
    <Compile Include="Assets\Client\Utility\DataGrid\ItemRender.cs" />
    <Compile Include="Assets\WGM\Common\Script\Load\Loader.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\GlobalCommon.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMDrawAnimData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\DeskInfoModule.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TwoLongTapMe.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISelectRoomItem.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\ParticleHandle.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIRecord.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\DbcsConvert.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ButtonScale.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\BaseConfig.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Hologram.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_TimeManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIOrthoCamera.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\ReplaceSuperTextRuntime.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\PropProtocol.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_ViewCenter.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\UINetOut.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_Notification.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\ViewManager.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_NullObjectWrap.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\JsonData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\MatchProtocol.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIBindPhone.cs" />
    <Compile Include="Assets\7zip\Compress\LZ\LzBinTree.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Info\InfoPanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\HallExchange\PrizeItem.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_StringWrap.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCInput.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Background\DealCommand.cs" />
    <Compile Include="Assets\Client\Controller\Command\SocketCommand.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\Poker\Poker.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_Test4.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\new\UIBetPanel.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Protocol\GameMessage.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletAutoClose.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_PlayerData.cs" />
    <Compile Include="Assets\WGM\Common\Script\Camera\ToolsCameraNGUI.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\Strings.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\Poker\Poker.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Ice.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TwoTapMe.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\VoiceChatPlayer.cs" />
    <Compile Include="Assets\7zip\Common\OutBuffer.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UEBossWhale.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\AutoFollow3Dto2DPaotai.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UECrabDrill.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIReport.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMDialogueSample.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaUnityLibs.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\SpineAttributes.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\CreateRoom\UICreateRoomRule.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\Interface\IKindInfo.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\UILogin.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\UniGifFormatter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\BankProtocol.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\MailProtocol.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_ButtonOnClick.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UE\UEFourOfAKindA.cs" />
    <Compile Include="Assets\WGM\ATT\Script\Player.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\Bullet.cs" />
    <Compile Include="Assets\Client\Utility\SequenceFrameAnim.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Tools\ServerList.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\LuaInterface_LuaInjectionStationWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNSocketEvent.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\TextVerticalGradientTwoColor.cs" />
    <Compile Include="Assets\Client\Utility\ScrollUV.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Liquid.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Player.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\Interface\IUserInfo.cs" />
    <Compile Include="Assets\CielaSpike\Thread Ninja\Ninja.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\HNLanguageType.cs" />
    <Compile Include="Assets\7zip\Compress\LZMA\LzmaEncoder.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\ReplaceFontRuntime.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishFreezed.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_Filcker.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_GameController.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaThread.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\Spawn.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_TweenScroll.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UI2DSpriteAnimation.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Script\UniWebViewTransitionEdge.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UITotalRecord.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\RankProtocol.cs" />
    <Compile Include="Assets\VisionTimer\vp_TimeUtility.cs" />
    <Compile Include="Assets\Reporter\ReporterGUI.cs" />
    <Compile Include="Assets\WGM\Common\Script\Camera\ToolsCameraUI.cs" />
    <Compile Include="Assets\WGM\Common\Script\Tween\TweenHighlight.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Transitional\LogoSwitchManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButtonOffset.cs" />
    <Compile Include="Assets\Client\ToLua\Core\ObjectTranslator.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\BoneData.cs" />
    <Compile Include="Assets\WGM\ATT\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIVoiceRecordView.cs" />
    <Compile Include="Assets\Client\Manager\ResourceManager.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\UI\newScroll.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\BG\BGBase.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\PerLight.cs" />
    <Compile Include="Assets\Client\ToLua\Misc\LuaLooper.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMDelayData.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDragResize.cs" />
    <Compile Include="Assets\Client\Utility\Mirror.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\PropertyBinding.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\UINewCompetition.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Player.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\TK2D\SpriteCollectionAttachmentLoader.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\PropWindow\PersonInfoWindow.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\JsonMapper.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubMember.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\Client\Utility\DebugLogin.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\DbcsEncoding.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\BuyDesk\BuyDeskControl.cs" />
    <Compile Include="Assets\WGM\NBA\Script\UI\UIDRecord.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\UISettlement.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMInputStringDemo.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Twist.cs" />
    <Compile Include="Assets\Client\ObjectPool\ObjectPool.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Room\SelfRoom\RoomErrorCode.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\Brick.cs" />
    <Compile Include="Assets\7zip\Compress\LZ\IMatchFinder.cs" />
    <Compile Include="Assets\WGM\Common\Script\Network\LoginHall.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\TextRoll.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Award\VipAwardLogic.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIOnLineAward.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\ScriptsForAssetBundleSystem\BaseLoader.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\RechargeController.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaMisc.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\AudioHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MyStruct\RoomInfo.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\Poker\Poker.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\HallExchange\ExchangControl.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\EasyTouch.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIEventTrigger.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Login\LoginCenterSever.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEBase.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_UICameraAdjustor.cs" />
    <Compile Include="Assets\Client\Utility\EnhanceScrollView\EnhanceScrollView.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_Loading.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Poker\Pai.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\ProtocalID.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ButtonToggle.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEFourOfAKindBAuthentic.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButton.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\AnimationState.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\UnityUI\UICompatibility\GlobalEasyTouchEvent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNRecordEvent.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_Collections_Generic_ListWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Loom.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Poker\Poker.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\GameExemple\RunBall\BallRunPlayer.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_UISaveTakeScorePanel.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaStatePtr.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_LightningBolt.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishRandomPuffer.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\BG\BGLantern.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCTouchPad.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\shop\Goods.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UISpriteData.cs" />
    <Compile Include="Assets\Client\Utility\EffectController.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_ResultManager.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\IkConstraintData.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIBetPanelQmyl1.cs" />
    <Compile Include="Assets\Client\Utility\ButtonHover.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\SuperTextMesh.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\ViewManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UICamera.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\EasyTouchInput.cs" />
    <Compile Include="Assets\Client\Manager\LuaManager.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityEyeConstraint.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\UIGlobalAssist.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\Manager.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\Tutorial5.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TooglePickMethodUI.cs" />
    <Compile Include="Assets\Client\Utility\DataGrid\UGUIClickHandler.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_TransformWrap.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNGameEvent.cs" />
    <Compile Include="Assets\WGM\ATT\Script\UI\UIDRecord.cs" />
    <Compile Include="Assets\Reporter\MultiKeyDictionary.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_ChipManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNDataSave.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UERoyalFlush.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Common\ProConstant.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Clipping.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\AtlasAttachmentLoader.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_CameraWrap.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UISpriteCollection.cs" />
    <Compile Include="Assets\Client\Utility\AutoSwitchLogo.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_DelegateWrap.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_Notification.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_RotateSelf.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\System\InvGameItem.cs" />
    <Compile Include="Assets\WGM\Common\Script\Camera\ToolsCameraCanvas.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Threshold.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\MCoroutine.cs" />
    <Compile Include="Assets\WGM\NBA\Script\UE\UEBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNMatchEvent.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\Withdrawal.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UIPanelBase.cs" />
    <Compile Include="Assets\Client\Utility\PlayAnim.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Login\InputNavigator.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\SkeletonUtility\SkeletonUtilityConstraint.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\UIWidget.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\WindowManager.cs" />
    <Compile Include="Assets\Client\Utility\ImageRaycastFilter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UINewRank.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBossCrocodile.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\GameInfoModule.cs" />
    <Compile Include="Assets\Client\Manager\PanelManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDragScrollView.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Hologram3.cs" />
    <Compile Include="Assets\WGM\Common\Script\Camera\ToolsCameraOverlay.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_SingletonMonoBase.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Event.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\NGUIMath.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEBigPrize.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\PayProtocol.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIPlayAnimation.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\UI\DeviceInfo.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNSafeQueue.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISpread.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIPay.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_BetItemManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_Light_Logic.cs" />
    <Compile Include="Assets\WGM\Common\Script\Camera\ToolsCameraMain.cs" />
    <Compile Include="Assets\Client\ToLua\Core\TypeTraits.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\UISliderColors.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishBombSameType.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UITotalRecordItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISelectRoom.cs" />
    <Compile Include="Assets\Client\Network\Net\ReceiveBuffer.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UISpriteAnimation.cs" />
    <Compile Include="Assets\Client\Manager\DownloadUrlManager.cs" />
    <Compile Include="Assets\WGM\ABackup\BenChiBaoMa\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\ExposedList.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_Loading.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Transitional\GloblePropWindow.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UILabel.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UECoin.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\UISnapshotPoint.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\Tween\TweenRotation.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaTable.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Demo\5.CallbackFromWeb\CallbackFromWebSceneManager.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\NGUIFont.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\LuaInterface_EventObjectWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\DllUtil.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\BG\BGCrocodile.cs" />
    <Compile Include="Assets\7zip\Compress\RangeCoder\RangeCoderBit.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_RendererWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\ThreadPoolSchedulerWorkerObjects.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\GameCreater\GameListAdapter.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCAxis.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNVipDeskEvent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNPositionNoticePanel.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\IkConstraint.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\STMReadoutControls.cs" />
    <Compile Include="Assets\Client\Utility\UIPlugins\GridLayoutGroupAnimation.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\Client\Framework\Core\Base.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_DataCenter.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\SkeletonGraphic.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_PhysicsWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\SingleThreadStarter.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_AudioClipWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubReview.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\OneFinger\LongTapMe.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenHeight.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ButtonSound.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\GameExemple\RunBall\ThirdPersonCamera.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\OneFinger\TapMe.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEExplodeBonus.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButtonMessage.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\ModifiedShadow.cs" />
    <Compile Include="Assets\Client\Framework\Core\Controller.cs" />
    <Compile Include="Assets\Client\Network\Net\GameSocket.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_StoneFX.cs" />
    <Compile Include="Assets\WGM\ATT\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\AdvancedExamples\AutoSelect\MultiLayerUI.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormSocket.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Info\AwardResultPanel.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\SuperControl\Item.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\UIQuitGameControl.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\UI\UIDRecord.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_MessagePrompt.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\Tween\TweenPosition.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\NGUIText.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Spread\Spread_PayInfo.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Room\SelfRoom\DeskInfoLogic.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\OldStruct\ComNameInfo.cs" />
    <Compile Include="Assets\7zip\ICoder.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MyStruct\KindInfo.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIKeyNavigation.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\CreateRoom\UICreateRoomRule_ShuangKouFour.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\SetColorPickerColor.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNArrayBuffer.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\SuperControl\UserItemControl.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\HNLogger.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishBoss.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\Localization.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Config\WebConfig.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEFiveBars.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDragCamera.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\UI\UIItem.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\SelectSwitch.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_Collections_Generic_KeyValuePairWrap.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\ChatInput.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UI\UIHallTable178.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\CustomMaterials\SkeletonRendererCustomMaterials.cs" />
    <Compile Include="Assets\Client\Utility\LanguageHelper.cs" />
    <Compile Include="Assets\WGM\Common\Script\Tween\TweenAround.cs" />
    <Compile Include="Assets\WGM\Common\Script\AudioPar\AudioHandleBase.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBossCrab.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\CreateRoom\UICreateRoomRule_LiKuiFishing3D.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_Tags.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\OpenURLOnClick.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_ShowFps.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Cannon\CannonLock.cs" />
    <Compile Include="Assets\VisionTimer\Examples\Scheduling\NonMonoBehaviour.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNPlatConstant.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\UnityUI\UICompatibility\ETWindow.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIPersonalCenter.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\ParticleHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Award\OnlineAwardLogic.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\AudioHandle.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\IUpdatable.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_SongDengAnim.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\ViewManager.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\JsonException.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\AudioHandle.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Json.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIPopupList.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\TextTip.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\BuildSceneBundle.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UECatcher.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\WindowDragTilt.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEBase.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\UISortBehavior.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\LoadLevelOnClick.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\UnityEngine_CoroutineWrap.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UEHappyTime.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UIBonus.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\HallFeature\FeatureConfig.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\Global.cs" />
    <Compile Include="Assets\Client\Network\ByteBuffer.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UITexture.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UIHistoryRecordPanel.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\JoystickParameter\AxisXUi.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\NetworkClient\BMW_NetworkHeartBeat.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\ShaderParticleScaler.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\PathAttachment.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\LongButton.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_Loading.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\AttachmentType.cs" />
    <Compile Include="Assets\WGM\Common\Script\SetProperty\Scripts\SetPropertyAttribute.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\AssetBundleStr.cs" />
    <Compile Include="Assets\Reporter\ReporterMessageReceiver.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIInputControl.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Config\PlatFormConfig.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Info\BetPanel.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\BackGround.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIHelp.cs" />
    <Compile Include="Assets\WGM\NBA\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\Client\Utility\ButtonEffect.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\ClubData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Http\ClientAsyncHttp.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_SingletonMonoBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubSetting.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\AutoFollow.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\VersionManager\AssetType.cs" />
    <Compile Include="Assets\Client\ToLua\Reflection\LuaReflection.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Liquify.cs" />
    <Compile Include="Assets\Client\ConstDefine\NotiConst.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\LuaInterface_LuaPropertyWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNBankEvent.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\SlotData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\IThreadWorkerObject.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\shop\GoodsManager.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\DemoPlayer.cs" />
    <Compile Include="Assets\WGM\Robot\JiqirenCanshu.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\BlendModeMaterialsAsset.cs" />
    <Compile Include="Assets\Client\Utility\LanguageLocalization.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\ClippingAttachment.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\SuperTextMeshData.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UITooltip.cs" />
    <Compile Include="Assets\VisionTimer\vp_Timer.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_ComponentWrap.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIFont.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Jelly.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_ResultData.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\ObjectPool.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_AssetBundleWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Common\NetProtocol\NetHead.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIWindowBase.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\Client\Manager\Interface\ITimerBehaviour.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\Dispatch\DispatchCallbackArg.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\WebUtility.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Pay\PayMgr.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\BlendMode.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\MainCamera.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ButtonToggleGroup.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\MailData.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\BG\BGBase.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_ViewCenter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Http\HTTPRequest.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_WWWWrap.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_GrayScale.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_MetalFX.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\MeshAttachment.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\WGM\NBA\Script\DemoPlayer.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_SoundSystem\HNAudioResource.cs" />
    <Compile Include="Assets\WGM\Common\Script\ChatViewAdaptMobileKeyBoard.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UECrabBase.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\GameExemple\RTSExample\RTSCamera.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UIScatterTip.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Player.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_BetItemManager.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\AllLight.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBossWhale.cs" />
    <Compile Include="Assets\Client\Manager\ThreadManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\PhraseItemButton.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UEBoss.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\TransformConstraint.cs" />
    <Compile Include="Assets\WGM\ATT\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\SkeletonDataModifierAsset.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\Social.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\ThreadWaitCommands.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishCrab.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Spread\Spreader_Staus.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\Poker\Poker.cs" />
    <Compile Include="Assets\Client\Utility\MonoBehaviourHelper.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaAttributes.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UIOnEnableTween.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_ArrayWrap.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Demo\3.LocalHTML\LocalHTMLSceneManager.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\Attachment.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIStandingItem.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_ButtonOnClick.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Plugins\ETCJoystick.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\AnimatedAlpha.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_RigidbodyWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\GB18030Encoding.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HallEvent\HNHallEventID.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_ShowFps.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\Game\EquipRandomItem.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\BMSymbol.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIBank.cs" />
    <Compile Include="Assets\WGM\Common\Script\Network\CommandMultipleBase.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\RechargeTip.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\Interface\IHallInfo.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\Robot\JiqirenDBXJ.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UECombineSameType.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\HorseRaceLamp.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\NetworkClient\DICE_NetworkHeartBeat.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButtonColor.cs" />
    <Compile Include="Assets\WGM\Common\Script\Tween\TweenNumber.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\Client\ToLua\Reflection\LuaMethod.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\SystemNotice\RealSysNoticeControl.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\UE\UEBase.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_ResourcesManager.cs" />
    <Compile Include="Assets\Client\ToLua\Core\ObjectPool.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Info\LinePanel.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\UIHall.cs" />
    <Compile Include="Assets\VisionTimer\Examples\StopWatch\StopWatch.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_Tags.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\LuaInterface_InjectTypeWrap.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\AudioHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_GameAdapter\UserDataTable.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UIRule.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIRoot.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\ReplaceSpriteRuntime.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\UIManager.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\UI\UIItem.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishRandomBig.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIColorPicker.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMAudioClipData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIMail.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaState.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIDragDropRoot.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\STMCustomInspectorTools.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ExtensionGameObject.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Blood.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_SoundSystem\HNAudioManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIToggledComponents.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\GameExemple\RTSExample\CubeSelect.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\ParticleHandle.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\UnityUI\UICompatibility\UICompatibility.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishCrabDrill.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletDrill.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\ParticleHandle.cs" />
    <Compile Include="Assets\VisionTimer\Examples\Scheduling\SchedulingDemo.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_Texture2DWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNBehaviourSingleton.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UESameType.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\TextVerticalGradientThreeColor.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEFiveOfAKind.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UECombine.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\TableAutoLoad.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\shop\GoodItem.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_EffectManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNMailEvent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\HallNetWorkMgr.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\DemoPlayer.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Spread\ISpreadMessage.cs" />
    <Compile Include="Assets\CielaSpike\Thread Ninja\TaskState.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\HNSocket.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Http\HTTPResponse.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UI\UIItem.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Sepia.cs" />
    <Compile Include="Assets\WGM\ATT\Script\DemoPlayer.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\UI\UIDRecord.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\PathConstraintData.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\SetSortingLayer.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\OldStruct\MSG_GP_R_LogonResult.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_GameUtility.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMTextureData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\HallExchange\HistoryItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\HNMessageID.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\AudioHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\AssetManager.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\UI\UIItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\MultithreadedWorkloadHelper.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\LuaInterface_LuaFieldWrap.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\MiniLitJson.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNVipResult.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIWidgetContainer.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Encryption\Encryption.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UIPanelBase.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenColor.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\AudioHandle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Room\SelfRoom\RoomSocket.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Logo.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\UE\UEBase.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\Game\UIEquipmentSlot.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\Tween\UITweener.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\JsonException.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\WWW\WWWHelper.cs" />
    <Compile Include="Assets\Client\Common\LuaLoader.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\DownloadTexture.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\TextSpacing.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_AudioManager.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\LoadingMgr.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UESameType.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\SkeletonDataAsset.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Animation\UITextureAnimationPro.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\OneFinger\TouchMe.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\System\InvEquipment.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\LitJson\JsonWriter.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\UE\UEPokerDeal.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNTools.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\AtlasAssetBase.cs" />
    <Compile Include="Assets\Client\ObjectPool\GameObjectPool.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\ChangeRQ.cs" />
    <Compile Include="Assets\Client\Utility\RenderQueue.cs" />
    <Compile Include="Assets\WGM\ATT\Script\UE\UEBase.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishBossJellyfish.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\RealTime.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Manager\AudioPlay.cs" />
    <Compile Include="Assets\WGM\HuanLeNiuNiu\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\WebUserInfoManager.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\Network\NetworkTest.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\Player.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_HistoryRecordItem.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEFourOfAKindL.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\Robot\JiqirenATT3.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\UserState.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenVolume.cs" />
    <Compile Include="Assets\testgame.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_GoldFX.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UE\UEExplodeBonus.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\ParticlesSize.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\ReportManager.cs" />
    <Compile Include="Assets\Client\Controller\Command\StartUpCommand.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_BetItemManager.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\UI\UIDRecord.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\NGUITools.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\ExampleDragDropItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\UniWebView\UniWebViewExt.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\UI\UIAdapter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\AndroidPlatformUtil.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubMain.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishProjection.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_ControlCenter.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\MainThreadWatchdog.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\UIGlobalBase.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\UI\UIDRecord.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Skin.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNReadFile.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\UI\UIMarquee.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\EncryptBundleEditor.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\TaskManager.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\UE\UEBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Encryption\UPKFolder.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\SkeletonUtility\SkeletonUtilityBone.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_EffectManager.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Demo\4.SizeAndTransition\SizeAndTransitionSceneManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\Gradient.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UEPassBonus.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\System\InvStat.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Common\NetProtocol\IProtocol.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\RotateGame\AllBonus.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\LuaBinder.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Smoke.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\XianLiaoManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\LoadAsset\AssetRequest.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Poker\PokerCard.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\BgAnim.cs" />
    <Compile Include="Assets\Client\Utility\SetTextureIndex.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMQuadData.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishPuffer.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_UIDicePanel.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNSignEvent.cs" />
    <Compile Include="Assets\Client\ToLua\Core\TypeChecker.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Script\UniWebView.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UE\UEFourOfAKind.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\EventHandleModel\EventCenter.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\GamePath.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNVipDeskInfoUI.cs" />
    <Compile Include="Assets\WGM\Common\Script\Auto\AutoTwinkleColor.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\BG\BGBase.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\Player.cs" />
    <Compile Include="Assets\WGM\TouBao\Script\UI\UIHandleQmyl1.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Bullet\BulletNet.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HallEvent\HNRankInfoEvent.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Mystic_Distortion.cs" />
    <Compile Include="Assets\WGM\DaZiBan\Script\ViewManager.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModulePhysics.cs" />
    <Compile Include="Assets\Client\Utility\EnhanceScrollView\EnhanceItem.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_SingletonBase.cs" />
    <Compile Include="Assets\WGM\RenYuChuanShuo\Script\UE\UERoyalFlush.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\AnimalCreater.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Demo\Common\Navigator.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Ragdoll\SkeletonRagdoll.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Noise.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\HNShowFPS.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_EffectManager.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\UI\OnButtonPressed.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\DPadClassicalTime\DPadParameterUI.cs" />
    <Compile Include="Assets\Client\Utility\ParticleSystemScaleUtil.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Prop\PropLogic.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIInput.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_Tools\UnityInterfaceImp.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\SettingPanel.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\initialize\HNPlatManager.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Heat.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\CreateRoom\UICreateRoom.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Bank\BankLogic.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaFileUtils.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\ReplaceTextRuntime.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\HallExchange\ExchangePanel.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_ResultManager.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\FaceAnimator.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_ObjectWrap.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\PlatFormProtocol.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_GlobalDefine.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Socket\HNSocketTemplate.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\WGM\Common\Script\Misc\RandData.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\OneFinger\DoubleTapMe.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_CameraClearFlagsWrap.cs" />
    <Compile Include="Assets\WGM\ATTZhiZun\Script\Poker\Poker.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_EnergyBar.cs" />
    <Compile Include="Assets\Client\Android\YsdkCallback.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UERandomBig.cs" />
    <Compile Include="Assets\Client\Utility\EnhanceScrollView\MyUGUIEnhanceItem.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UI\UIHandle.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_Collections_Generic_Dictionary_KeyCollectionWrap.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_NoiseAnimated.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\ECamera.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleSprite.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Ghost.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\ViewManager.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishNormal.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\AnimationMatchModifier\AnimationMatchModifierAsset.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishTurtle.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\LoadAsset\AssetLoading.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Event\HNPropEvent.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaStatic.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\ResourceCacheManager.cs" />
    <Compile Include="Assets\Client\Manager\LanguageManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\EventHandleModel\EventReceiver.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_GrassFX.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\Component\EasyTouchTrigger.cs" />
    <Compile Include="Assets\7zip\Compress\RangeCoder\RangeCoder.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\AudioHandle.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipTrack.cs" />
    <Compile Include="Assets\Client\Utility\ReflectionHelper.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\UE\UEBase.cs" />
    <Compile Include="Assets\Client\Utility\BlurMask.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\Spin.cs" />
    <Compile Include="Assets\WGM\ATTManHuaBan\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\UnityUI\UICompatibility\UIWindow.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaStackOp.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_ColorRGB.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_CompressionFX.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_EnumWrap.cs" />
    <Compile Include="Assets\WGM\JinHuangGuan\Script\UE\UEBase.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_SingletonBase.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Cartoon.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClub.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Scripts\Modules\STMFontData.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\Tween\TweenScale.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\DownAsset\AssetMultiDown.cs" />
    <Compile Include="Assets\Client\Utility\RandomAnimation.cs" />
    <Compile Include="Assets\WGM\Common\Script\Load\LoadSlider.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNJson.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Triangulator.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISingleRecord.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SlotBlendModes\SlotBlendModes.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\UI\UIHallTable.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_CardManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UIViewport.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIStandingRecord.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\UI\UIMarquee.cs" />
    <Compile Include="Assets\OtherPackage\UniWebView\Demo\6.RunJavaScriptInWeb\RunJavaScriptInWebSceneManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\TimeManager.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_GrassMultiFX.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\SimpleExamples\TwoFingers\TwoTouchMe.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\UnityEngine_ObjectWrap.cs" />
    <Compile Include="Assets\Client\QQChart\QQChartUtil.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UEGainScore.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButtonKeys.cs" />
    <Compile Include="Assets\Client\Network\Net\SocketConst.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNFileTools.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UIMoneyExchange.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_ResourcesManager.cs" />
    <Compile Include="Assets\WGM\HuoFengHuang\Script\UI\Blueeboard.cs" />
    <Compile Include="Assets\WGM\DaTingG178\Script\UI\MoneySend.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\DemoPlayer.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\PerspectivePixelPerfect.cs" />
    <Compile Include="Assets\Client\ConstDefine\AppConst.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNRegularTools.cs" />
    <Compile Include="Assets\7zip\Compress\LZMA\LzmaDecoder.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUI.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UISelecter.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Asset Types\EventDataReferenceAsset.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Localization\DataItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\EventHandleModel\Interface\IEvent.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\GameMain\DT_ButtonBase.cs" />
    <Compile Include="Assets\7zip\Compress\LZ\LzInWindow.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_SingletonMonoBase.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Shiny_Reflect.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISpeadReward.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_TimeManager.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaException.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\ByteEncoding.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\MinMaxRangeAttribute.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Frame\UIScatterResult.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\FSM\StateMachine.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubNotice.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\CreateRoom\UICreateRoomRule_TheMermaid3D.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\UI\UIBetPanel.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouchControls\Examples\_Medias\SliderText.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\UniGif.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Distortion.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIToggledObjects.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNXmlHelper.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_PlayerDataManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\Platfrom\Award\FirstRechargeAward.cs" />
    <Compile Include="Assets\7zip\Compress\LZ\LzOutWindow.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\Other\WindowAutoYaw.cs" />
    <Compile Include="Assets\Client\Android\Pay.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\PayConfig.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_ScreenAdapt.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Bullet\BulletHappyTime.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_DataCenter.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_ShaderWrap.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\SkeletonClipping.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\Player.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\BG\BGOctopus.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UECoin.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\UE\UEBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISysNoticeItem.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\Test\BMW_Test.cs" />
    <Compile Include="Assets\WGM\JinShaYinSha\Script\ViewManager.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\SkeletonBinary.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNUIPropPanel.cs" />
    <Compile Include="Assets\Client\ToLua\BaseType\System_Collections_IEnumeratorWrap.cs" />
    <Compile Include="Assets\Client\Utility\CandyCaptureGemCellDestroy.cs" />
    <Compile Include="Assets\WGM\SanSeLong\Script\AudioHandle.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIShowControlScheme.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenLetters.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_BetItem.cs" />
    <Compile Include="Assets\WGM\Common\Script\Network\KHttpDownloader.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\AnimationStateData.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_Chip.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Misc\ProjectToPlane.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_MonoBehaviourWrap.cs" />
    <Compile Include="Assets\NGUI\Examples\Scripts\InventorySystem\System\InvBaseItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubItem.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Slim.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\CenterProtocol.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_Sharpen.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\GameMain\BMW_ErrorPrompt.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Common\Interface\IUpdateable.cs" />
    <Compile Include="Assets\WGM\ATT3\Script\UE\UEBase.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Hall\PlatFormProtocol\RecordProtocol.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Attachments\PointAttachment.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Manager\GameResLoader.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_ScreenWrap.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\GameExemple\RunBall\Ball.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_IcedFX.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\AudioHandle.cs" />
    <Compile Include="Assets\WGM\HaiWang2\Script\Fish\FishCreater.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\Test\DICE_GlobalDefine.cs" />
    <Compile Include="Assets\Client\Network\Net\ByteArray.cs" />
    <Compile Include="Assets\Client\Utility\TextEff\ListPool.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\LetterSpacing.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\UISign.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Timer\UpdateTimer.cs" />
    <Compile Include="Assets\Client\Framework\Interfaces\IController.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\Club\UIClubRoomHistory.cs" />
    <Compile Include="Assets\Client\Framework\Core\Facade.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UIRobPanel.cs" />
    <Compile Include="Assets\WGM\XiYouZhengBa\Script\UI\UIGlobal.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_BlackHole.cs" />
    <Compile Include="Assets\Client\Utility\TweenerAnimation.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Encode\GB18030Source.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\ViewManager.cs" />
    <Compile Include="Assets\Client\Manager\NetworkManager.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_AudioManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UniGif\UniGifDecoder.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleAudio.cs" />
    <Compile Include="Assets\WGM\NBA\Script\Robot\JiqirenNBATable.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_UIHomepagePanel.cs" />
    <Compile Include="Assets\Client\Manager\Notification\NotificationMessageScripts.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\GameMain\DICE_HistoryRecordItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\AssertManager\LoadAsset\IAssetLoading.cs" />
    <Compile Include="Assets\WGM\Common\Script\Auto\AutoFadeOut.cs" />
    <Compile Include="Assets\WGM\CaiJinDanTiao\Script\Background\DealCommand.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaMatchType.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\UINewCompetitionItem.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UI2DSprite.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\Animation\SpriteAdapter.cs" />
    <Compile Include="Assets\Client\Framework\Core\Manager.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Atlas.cs" />
    <Compile Include="Assets\WGM\DBXJ\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\Client\Utility\ButtonPressUp.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_HSV.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIPlaySound.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishCombine.cs" />
    <Compile Include="Assets\Client\Utility\CaptureGemCellDestroy.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GameStart.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\SkeletonBounds.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Ghost\SkeletonGhostRenderer.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Roll\RollColumn.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\NativeWebView\NativeWebView.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\BoundingBoxFollower\BoundingBoxFollower.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\Interface\ICommercer.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\Test\DT_NotificationCenter.cs" />
    <Compile Include="Assets\WGM\Common\Script\UI\RefshSliderLight.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaBaseRef.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\AnimatedColor.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenTransform.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\UE\UEBombLocal.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Ghost\SkeletonGhost.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DT\Scripts\General\DT_ButtonOnClick.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Example\UnityUI\UITwistPinch\UIDrag.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Location\LocationUtil.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\Dispatch\DispatchCallBackArgRturn.cs" />
    <Compile Include="Assets\Client\ToLua\Core\LuaFunction.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_ScreenAdapt.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\ExtensionTransform.cs" />
    <Compile Include="Assets\WGM\LongTaiZi\Script\UI\UIShowTip.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\NativeWebView\AndroidWebView.cs" />
    <Compile Include="Assets\Fireworks\Scripts\Demo\SmoothCameraOrbit.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Hall\HallPropWindow\CreateRoom\UICreateRoomRule_DeZhouPoker.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishBombLocal.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\Bone.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UICenterOnChild.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Protocol\GameMessageEvent.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Game\Common\HNParticleItem.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ClientPlat\Room\PCRoom\PCProtocolID.cs" />
    <Compile Include="Assets\OtherPackage\LitJson\ParserToken.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\Dispatch\DispatchAction.cs" />
    <Compile Include="Assets\WGM\SlotsFruit\Scripts\Manager\GameManager.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_DICE\Scripts\General\DICE_GlobalManager.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\UITweener.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\UI\HallExchange\PrizeItemControl.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\UIGeometry.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIButtonActivate.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\ISkeletonAnimation.cs" />
    <Compile Include="Assets\NGUI\Scripts\UI\UISprite.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-csharp\IConstraint.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\Player.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Utility\STMChangeSortingOrder.cs" />
    <Compile Include="Assets\WGM\XingYunLiuShi\Script\UE\UEBase.cs" />
    <Compile Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\BMW_AudioManager.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Loom\Internal\Dispatch\DispatchCallbackReturn.cs" />
    <Compile Include="Assets\Client\Utility\ButtonPressDown.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\UIKeyBinding.cs" />
    <Compile Include="Assets\WGM\Common\Script\SetProperty\Scripts\SetPropertyExample.cs" />
    <Compile Include="Assets\2DxFX\Scripts\_2dxFX_DesintegrationFX.cs" />
    <Compile Include="Assets\WGM\BenChiBaoMa\Script\BG\BGBase.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipClip.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Utils\HNIpv6.cs" />
    <Compile Include="Assets\WGM\CommonFish\Script\Fish\FishFormation.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\HNPlat\Toos\HNIniFile.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\Common\Hall\_ShareData\OldStruct\ComRoomInfo.cs" />
    <Compile Include="Assets\OtherPackage\xzhuan\HNFRW\GUIExtensions\UITimeText.cs" />
    <Compile Include="Assets\NGUI\Scripts\Interaction\TypewriterEffect.cs" />
    <Compile Include="Assets\NGUI\Scripts\Internal\NGUIDebug.cs" />
    <Compile Include="Assets\EasyTouchBundle\EasyTouch\Plugins\Gesture.cs" />
    <Compile Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\AttachmentTools\AttachmentTools.cs" />
    <Compile Include="Assets\WGM\ATT\Script\Background\LibAlg.cs" />
    <Compile Include="Assets\NGUI\Scripts\Tweening\TweenRotation.cs" />
    <Compile Include="Assets\Client\ToLua\Reflection\LuaField.cs" />
    <Compile Include="Assets\Client\ToLua\Source\Generate\UnityEngine_ColliderWrap.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_777_1\skeleton.atlas.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\Fonts\Itim\OFL.txt" />
    <None Include="Assets\CommDT\Shader\1Background-2Refractors.shader" />
    <None Include="Assets\EasyTouchBundle\Readme.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\huofenghuang\huangfenghuang.atlas.txt" />
    <None Include="Assets\Client\BuildResource\GameCandy\candyImg\candyFont\candyStoreRMBFont\candyStoreRMBFont.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Mystic_Distortion_Additive.shader" />
    <None Include="Assets\WGM\HuoFengHuang\2DObject\Player\Font\BlueboardYellow\篮板黄字.fnt" />
    <None Include="Assets\OtherPackage\Spine\version.txt" />
    <None Include="Assets\UI\Head\Head.txt" />
    <None Include="Assets\WGM\SlotsFruit\MainLayer\num\SGFreeScroe.fnt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\Catcher\Number\闪电连锁倍数.fnt" />
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_gaoyaguo\skeleton.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Slim.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\jinshayinsha\jinshayinsha.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_WoodFX.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonRenderSeparator\SkeletonRenderSeparator.txt" />
    <None Include="Assets\WGM\HuoFengHuang\2DObject\Player\Font\GetScoreYellow\取分黄字.fnt" />
    <None Include="Assets\OtherPackage\UniWebView\ChangeLog.txt" />
    <None Include="Assets\NGUI\Examples\Atlases\Wooden\Arimo20.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Resources\Shaders\Spine\Skeleton.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\bairenniuniu\bairenniuniu.atlas.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\att\att.atlas.txt" />
    <None Include="Assets\OtherPackage\UniWebView\Readme.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\daziban\daziban.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Threshold.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Lit\Lit Standard.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\777\777.atlas.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\shaiziwang\shaiziwang.atlas.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\kongzhan\kongzhan.atlas.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\shenlongbaozang\BY_HBJC_ske.atlas.txt" />
    <None Include="Assets\Client\BuildResource\GameSlotsFruit\SlotsFruitFont\FreeTimesFont\FreeTimesFont.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\Utility\HiddenPass.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\niumowang\niumowang.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Liquid.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\bazhuayu\bazhuayu.atlas.txt" />
    <None Include="Assets\NGUI\Examples\Models\Orc\Orc Skin.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\ShaderShared.cginc" />
    <None Include="Assets\NGUI\Examples\Atlases\SciFi\SciFi Font - Header.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_StoneFX.shader" />
    <None Include="Assets\Hall\Resources\BaseConfigTxt.xml" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Ice.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Packed 3.shader" />
    <None Include="Assets\NGUI\Examples\Shaders\Unlit - Depth Cutout.shader" />
    <None Include="Assets\CommDT\Shader\Particle Add Scale.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\text_bg\text_bg.atlas.txt" />
    <None Include="Assets\LZQ\Wei\Game_DICE\Fonts\DICE_Num4.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_GoldFX.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\ShaderMaths.cginc" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteSpecular.cginc" />
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_dawanjia\skeleton.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Heat.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\shuangxiangqie\shuangxiangqie.atlas.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\paiji\paiji.atlas.txt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\CrabLaser\Misc\激光倒计时.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Additive.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Masked 3.shader" />
    <None Include="Assets\EasyTouchBundle\EasyTouchControls\Readme.txt" />
    <None Include="Assets\CommDT\Shader\Outline.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\DefaultMedia\Credits.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\jinchanbuyu\jinchanbuyu.atlas.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\shuangxiang\shuangxiangjinlongyu.atlas.txt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Spine-Skeleton-Tint.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_GrayScale.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Colored (TextureClip).shader" />
    <None Include="Assets\Client\BuildResource\GameCandy\candyImg\candyFont\candyTotalScore\candytotalscore.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_SkyCloud.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteVertexLighting.cginc" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_CircleFade.shader" />
    <None Include="Assets\WGM\Common\2DObject\Player\Font\Num1\玩家总分0.fnt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Unlit\Unlit Dropshadow.shader" />
    <None Include="Assets\CommDT\Shader\Transparent Masked.shader" />
    <None Include="Assets\Client\BuildResource\GameCandy\candyImg\candyFont\commonFont\CommonFont.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Cartoon.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_IcedFX.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\UI\UI Dropshadow Outline.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_DestroyedFX.shader" />
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_777_2\skeleton.atlas.txt" />
    <None Include="Assets\CommDT\Shader\LightSweep.shader" />
    <None Include="Assets\Fireworks\Fireworks - Readme.txt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CameraNormalsTexture.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\BlendModes\Spine-Skeleton-PMA-Additive.shader" />
    <None Include="Assets\CommDT\font\shuzinew\shuzinew.fnt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Unlit\Unlit Outline.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\Ionic.Zlib.dll" />
    <None Include="Assets\WGM\HuoFengHuang\2DObject\Player\Font\BlueboardCrapsYellow\比倍篮板黄字.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Masked.shader" />
    <None Include="Assets\CommPJ\Player\Font\黑板白字.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Premultiplied Colored 1.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Wave.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Pixel8bitsBW.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\benz\benz.atlas.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\caishenfafafa\caisehenfafafa_2.atlas.txt" />
    <None Include="Assets\Client\BuildResource\GameSlotsFruit\SlotsFruitFont\JieSuanFont\JieSuanFont.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_CompressionFX.shader" />
    <None Include="Assets\WGM\Common\2DObject\Player\Font\Num1\玩家总分.fnt" />
    <None Include="Assets\LZQ\Wei\Game_BMW\Fonts\BMW_Num5.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Colored (Packed) (TextureClip).shader" />
    <None Include="Assets\WGM\ABackup\TouWang\2DObject\Font\玩家总分\玩家总分.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Packed.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_WaterAndBackground.shader" />
    <None Include="Assets\NGUI\Examples\Shaders\Unlit - Depth.shader" />
    <None Include="Assets\Demigiant\readme_DOTweenPro.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\buyudaheng\buyudaheng.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Negative.shader" />
    <None Include="Assets\Demigiant\DOTween\DOTween.XML" />
    <None Include="Assets\LZQ\Wei\Game_DICE\Fonts\DICE_Num1.fnt" />
    <None Include="Assets\Client\ToLua\Injection\InjectionBlackList.txt" />
    <None Include="Assets\WGM\ABackup\BenChiBaoMa\2DObject\Font\倒计时.fnt" />
    <None Include="Assets\Demigiant\DemiLib\Core\DemiLib.dll" />
    <None Include="Assets\Client\BuildResource\GameSlotsFruit\SlotsFruitFont\LeiJiAwardFont\LeiJiAwardFont.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritesUnlit.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\sanselong\sanselong.atlas.txt" />
    <None Include="Assets\LZQ\Wei\Game_BMW\Scripts\General\Debuger.dll" />
    <None Include="Assets\LZQ\178\Game_BMW\Fonts\BMW_Num2.fnt" />
    <None Include="Assets\Client\BuildResource\GameCandy\candyImg\candyFont\candyCoinFont\CandyCoinFont.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\AnimationMatchModifier\AnimationMatchModifierAsset.txt" />
    <None Include="Assets\CommPJ\Player\Font\BlueboardCrapsWhite\比倍篮板白字.fnt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Sample\Fonts\Walibi\Credits.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Hologram2.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Packed 2.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数7.fnt" />
    <None Include="Assets\NGUI\ReadMe - 2019.3.0.txt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteLighting.cginc" />
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_GOOD LUCK\skeleton.atlas.txt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\CrabLaser\Misc\激光获得分数.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Spine-Skeleton-Fill.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Blood.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Clipping.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_WaterAndBackgroundDeluxe.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Premultiplied Colored 2.shader" />
    <None Include="Assets\CommPJ\Player\Font\比倍白字.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_PlasmaShield.shader" />
    <None Include="Assets\LZQ\178\Game_BMW\Fonts\BMW_Num7.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Hologram3.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\Catcher\Number\闪电连锁获得分数.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Colored 2.shader" />
    <None Include="Assets\Fireworks\Scripts\Simple Particle Scale\Simple Particle Scale - Readme.txt" />
    <None Include="Assets\WGM\HuoFengHuang\2DObject\Player\Font\BlueboardBlue\篮板蓝字.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Distortion_Additive.shader" />
    <None Include="Assets\CommDT\Shader\Particle AddSmooth.shader" />
    <None Include="Assets\CommDT\Shader\Sprite.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Waterfall.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\UI\UI Outline.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\Utility\Hidden-Spine-Bones.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\yulehongxie\yulehongxie.atlas.txt" />
    <None Include="Assets\CommDT\Shader\水纹.shader" />
    <None Include="Assets\NGUI\Examples\Atlases\Refractive\Refractive Font - Normal.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_HSV.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\caijindantiao\caijindantiao.atlas.txt" />
    <None Include="Assets\CommDT\Shader\Gray.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Masked 2.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\CrabBomb\Frame\炸弹蟹分数框数字.fnt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数3.fnt" />
    <None Include="Assets\CommDT\Shader\SpriteFlash.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Smoke.shader" />
    <None Include="Assets\LZQ\Wei\Game_BMW\Fonts\BMW_Num6.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteUnlit.cginc" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\UI\UI.shader" />
    <None Include="Assets\StreamingAssets\build_info.txt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\CrabBomb\Atlas\炸弹蟹获得分数.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_MetalFX.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\Shaders\Spine-SkeletonGraphic-TintBlack.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteUnlit.cginc" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Text 3.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\CrabDrill\Frame\钻头炮分数框数字.fnt" />
    <None Include="Assets\LZQ\178\Game_DT\Fonts\DT_Num1.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Text 2.shader" />
    <None Include="Assets\LZQ\Wei\Game_DICE\Fonts\DICE_Num5.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CameraDepthNormalsTexture.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\nba\nba.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_BurningFX.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Text.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Premultiplied Colored (TextureClip).shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Color.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\SDF\SDF Unlit.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\renyuchuanshuo\renyuchuanshuo.atlas.txt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数4.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteShadows.cginc" />
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_jinbizuoyi\skeleton.atlas.txt" />
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_777_3\skeleton.atlas.txt" />
    <None Include="Assets\StreamingAssets\LoginInfo.txt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteVertexLighting.cginc" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\SDF\SDF Unlit Dropshadow.shader" />
    <None Include="Assets\CommDT\Shader\WaterWaveEffect.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Colored.shader" />
    <None Include="Assets\CommPJ\Player\Font\总分白字.fnt" />
    <None Include="Assets\LZQ\Wei\Game_BMW\Fonts\BMW_Num2.fnt" />
    <None Include="Assets\WGM\DaTingG178\Spine\manhuaban\manhuaban.atlas.txt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SameType\Atlas\旋风鱼获得分数.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CameraDepthTexture.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数6.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Frozen.shader" />
    <None Include="Assets\LZQ\Wei\Game_BMW\Fonts\BMW_Num4.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Packed 1.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\UI\UI Dropshadow.shader" />
    <None Include="Assets\EasyTouchBundle\EasyTouchControls\ReleaseNotes.txt" />
    <None Include="Assets\WGM\CaiJinDanTiao\2DObject\Font\彩金数字.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Ghost\Shaders\Spine-Special-Skeleton-Ghost.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritePixelLighting.cginc" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\UI\UI Pixel Snap.shader" />
    <None Include="Assets\Fireworks\Scripts\C# Scripts\- IMPORTANT - Version Change Readme.txt" />
    <None Include="Assets\LZQ\178\Game_BMW\Fonts\BMW_Num6.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity.txt" />
    <None Include="Assets\Client\BuildResource\GameShz\ShzFont\ShzHelpFont\shzHelpFont.fnt" />
    <None Include="Assets\Demigiant\DOTween\DOTween.dll" />
    <None Include="Assets\NGUI\Examples\Resources\Localization.txt" />
    <None Include="Assets\Client\BuildResource\GameSlotsFruit\SlotsFruitFont\JieSuanFont\ShuihuzhuanshoufenFont.fnt" />
    <None Include="Assets\Client\ToLua\readme.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_4Gradients.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\BossCommon\Number\玻璃球数字.fnt" />
    <None Include="Assets\LZQ\178\Game_BMW\Fonts\BMW_Num5.fnt" />
    <None Include="Assets\222222222222\20240606\res\caijinNum1.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Teleportation.shader" />
    <None Include="Assets\Client\BuildResource\GameCandy\candyImg\candyFont\candyAwardFont\candyAwardFont.fnt" />
    <None Include="Assets\Hall\Resources\Localization.txt" />
    <None Include="Assets\NGUI\Examples\Atlases\Wooden\Arimo18.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\shiqianjue\shiqianjue.atlas.txt" />
    <None Include="Assets\WGM\NBA\2DObject\Font\caijinFnt.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_GoldenFX.shader" />
    <None Include="Assets\WGM\CaiJinDanTiao\2DObject\Font\倒计时数字.fnt" />
    <None Include="Assets\CommDT\Shader\MaskIcon.shader" />
    <None Include="Assets\Client\BuildResource\GameShz\ShzFont\ShzJiangChiFont\ShzJiangChiFont.fnt" />
    <None Include="Assets\WGM\Common\2DObject\Font\大厅金字.fnt" />
    <None Include="Assets\UI\Commom\Commom.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Unlit\Unlit Ontop.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Liquify.shader" />
    <None Include="Assets\Client\BuildResource\GameSlotsFruit\SlotsFruitFont\WinBeiJingFont\WinBeiJingFont.fnt" />
    <None Include="Assets\NGUI\Examples\Atlases\Refractive\Refractive Font - Header.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Resources\Shaders\Spine\SkeletonGhost.shader" />
    <None Include="Assets\LZQ\Wei\Game_DT\Fonts\DT_Num1.fnt" />
    <None Include="Assets\CommDT\old\普通得分.fnt" />
    <None Include="Assets\NGUI\Examples\Shaders\Refractive.shader" />
    <None Include="Assets\NGUI\Examples\Shaders\Unlit - Masked Colored.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数5.fnt" />
    <None Include="Assets\WGM\NBA\2DObject\Font\pico2x.fnt" />
    <None Include="Assets\CommDT\Texture\Atlas\sprite.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Hologram.shader" />
    <None Include="Assets\Client\BuildResource\GameShz\ShzFont\ShzbeautyFont\ShzBeautyFont.fnt" />
    <None Include="Assets\CommDT\Shader\SpriteRollUV.shader" />
    <None Include="Assets\LZQ\Wei\Game_BMW\Fonts\BMW_Num1.fnt" />
    <None Include="Assets\NGUI\Examples\Atlases\SciFi\SciFi Font - Normal.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_ColorRGB.shader" />
    <None Include="Assets\LZQ\Wei\Game_DT\Fonts\DT_Num0.fnt" />
    <None Include="Assets\Client\ToLua\Injection\InjectionBridgeEditorInfo.xml" />
    <None Include="Assets\WGM\DaTingG178\Spine\tiejiafeilong\tiejiafeilong.atlas.txt" />
    <None Include="Assets\UI\NewHall\Alts\NewHall.txt" />
    <None Include="Assets\LZQ\178\Game_DT\Fonts\DT_Num0.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_GrassMultiFX.shader" />
    <None Include="Assets\Demigiant\DOTweenPro\DOTweenPro.dll" />
    <None Include="Assets\Demigiant\DOTweenPro\readme.txt" />
    <None Include="Assets\WGM\DaTingG178\Spine\yaoqianshu\yaoqianshu.atlas.txt" />
    <None Include="Assets\CommPJ\Player\Font\彩金红字.fnt" />
    <None Include="Assets\CommDT\Comom02\Commom02.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\UI\UI Masked.shader" />
    <None Include="Assets\WGM\BuYuDaHeng\2DObject\background\水纹.shader" />
    <None Include="Assets\LZQ\178\Game_BMW\Fonts\BMW_Num4.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Spine-Special-Skeleton-Grayscale.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\SkeletonGraphic\Shaders\Spine-SkeletonGraphic.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Sepia.shader" />
    <None Include="Assets\Client\ToLua\Injection\LuaInjectionSkipPaths.txt" />
    <None Include="Assets\StreamingAssets\init.txt" />
    <None Include="Assets\LZQ\178\Game_BMW\Fonts\BMW_Num1.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Pixel.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Fire.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\BlendModes\Spine-Skeleton-PMA-Multiply.shader" />
    <None Include="Assets\WGM\HuoFengHuang\2DObject\Player\Font\BonusYellow\彩金黄字.fnt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数0.fnt" />
    <None Include="Assets\Demigiant\DemiLib\Core\DemiLib.xml" />
    <None Include="Assets\WGM\HuoFengHuang\2DEffect\Win\WinBlueBig\篮板蓝字大.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteDepthNormalsTexture.shader" />
    <None Include="Assets\NGUI\Examples\Atlases\Wooden\Arimo14.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\SDF\SDF Unlit DropshadowAndOutline.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\bashanaohai\bashanaohai.atlas.txt" />
    <None Include="Assets\WGM\HuoFengHuang\2DEffect\Win\WinYellowBig\篮板黄字大.fnt" />
    <None Include="Assets\Demigiant\DOTweenPro\DOTweenPro.XML" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteLighting.cginc" />
    <None Include="Assets\CommDT\Shader\Transparent Colored Gray.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Masked 1.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritesVertexLit.shader" />
    <None Include="Assets\Client\BuildResource\GameShz\ShzFont\shzStoreFont\ShzStoreFont.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_EnergyBar.shader" />
    <None Include="Assets\WGM\SlotsFruit\GameResource\水果机_特效\shuiguoji\donghua_tanchuzi\skeleton.atlas.txt" />
    <None Include="Assets\WGM\HuoFengHuang\2DObject\Player\Font\MultipleGold\倍数金字.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Twist.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Noise.shader" />
    <None Include="Assets\MatiarlsForTestDAjinAnZhuangBao\TestShader.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Colored 3.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Sharpen.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\xiyouzhengba\ziyouzhengba.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Posterize.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Unlit\Unlit Pixel Snap.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\zuanshidaheng\zuanshidaheng.atlas.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Lit\Lit Lambert.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Lightning.shader" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\Hall\Resources\Shaders\Spine\SkeletonGraphic.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Blur.shader" />
    <None Include="Assets\WGM\HaiWang2\Prefab\Particle\Effect\Laser\Lase\JMO Assets\Cartoon FX\Shaders (Mobile)\CFXM_MobileParticleAdd_Alpha8.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\RandomBig\Number\浮动倍率大鱼数字.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_LightningBolt.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_PlasmaRainbow.shader" />
    <None Include="Assets\Demigiant\DOTween\readme.txt" />
    <None Include="Assets\LZQ\178\Game_DT\Fonts\DT_Num2.fnt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\Ionic.Zlib.Agreement.txt" />
    <None Include="Assets\WGM\BuYuDaHeng\2DObject\Player\Font\Num1\玩家总分.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\CustomMaterials\SkeletonRendererCustomMaterials.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Pixel8bitsC64.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\sanseeyu\saseeyu.atlas.txt" />
    <None Include="Assets\StreamingAssets\game.txt" />
    <None Include="Assets\Client\BuildResource\GameShz\ShzFont\ShzFont1\RedFont.fnt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Unlit\Unlit.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_NoiseAnimated.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\bg_light\bg_light.atlas.txt" />
    <None Include="Assets\LZQ\Wei\Game_DICE\Fonts\DICE_Num2.fnt" />
    <None Include="Assets\WGM\DaTingG178\Spine\jinhuangguan\jinhuangguan.atlas.txt" />
    <None Include="Assets\WGM\BuYuDaHeng\2DObject\Player\Font\Num1\玩家总分0.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Jelly.shader" />
    <None Include="Assets\NGUI\Examples\Atlases\Fantasy\Fantasy Font - Normal.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Distortion.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\Spine-SkeletonLit.shader" />
    <None Include="Assets\WGM\BuYuDaHeng\2DObject\Player\Font\Num2\押分数字.fnt" />
    <None Include="Assets\NGUI\Examples\Models\Orc Armor\Orc Armor.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Premultiplied Colored.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Pixel8bitsGB.shader" />
    <None Include="Assets\WGM\Common\2DObject\Player\Font\Num2\押分数字.fnt" />
    <None Include="Assets\WGM\NBA\2DObject\Font\caijinNum_1.fnt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\Catcher\Number\闪电连锁条数.fnt" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数1.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\Spine-Skeleton.shader" />
    <None Include="Assets\Client\BuildResource\GameCandy\candyImg\candyFont\candyStoreDiamondFont\candyStoreDiamondFont.fnt" />
    <None Include="Assets\WGM\NBA\2DObject\Font\caijinTOPfnt.fnt" />
    <None Include="Assets\LZQ\178\Game_BMW\Fonts\BMW_Num3.fnt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\SDF\SDF Unlit Outline.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\luckylion\luckylion.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_PatternAdditive.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Pattern.shader" />
    <None Include="Assets\CommDW\数码管数字.fnt" />
    <None Include="Assets\WGM\DaTingG178\Spine\chaojizhizun2\chaojizhizun2.atlas.txt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\BlendModes\Spine-Skeleton-PMA-Screen.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Premultiplied Colored 3.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Mystic_Distortion.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_SandFX.shader" />
    <None Include="Assets\WGM\HuoFengHuang\2DEffect\FourOfAKind\Number\金蛋数字.fnt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Unlit\Unlit DropshadowAndOutline.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_DesintegrationFX.shader" />
    <None Include="Assets\WGM\ShangHaiSaiChe\2DObject\Font\数码管带点数字.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\ShaderShared.cginc" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Outline.shader" />
    <None Include="Assets\WGM\HaiWang2\2DEffect\SmallCommon\Number\小鱼分数2.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Ghost.shader" />
    <None Include="Assets\Client\BuildResource\GameShz\ShzFont\ShzRoomFont\ShzRoomFont.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Shaders\Spine-Skeleton-TintBlack.shader" />
    <None Include="Assets\LZQ\Wei\Game_DICE\Fonts\DICE_Num3.fnt" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Text (TextureClip).shader" />
    <None Include="Assets\WGM\DaTingG178\Texture\Atlas\icon.txt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpriteShadows.cginc" />
    <None Include="Assets\NGUI\Examples\Shaders\Unlit - Additive Colored.shader" />
    <None Include="Assets\LZQ\Wei\Game_BMW\Fonts\BMW_Num3.fnt" />
    <None Include="Assets\Client\BuildResource\GameCandy\candyImg\candyFont\candyScoreFont\CandyScoreFont.fnt" />
    <None Include="Assets\WGM\DaTingG178\Spine\fruitparty\fruit.atlas.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_FireAdditive.shader" />
    <None Include="Assets\WGM\DaTingG178\Spine\likuipiyu\likuipiyu.atlas.txt" />
    <None Include="Assets\OtherPackage\xzhuan\HNFRW\ThirdPartyPlugin\Clavian\SuperTextMesh\Resources\Shaders\Unlit\Unlit Cull Back.shader" />
    <None Include="Assets\WGM\SlotsFruit\MainLayer\num\myFont.fnt" />
    <None Include="Assets\EasyTouchBundle\EasyTouch\RealseNotes.txt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_Shiny_Reflect.shader" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\SpritesPixelLit.shader" />
    <None Include="Assets\Client\BuildResource\GameShz\ShzFont\ShzLeiJiJiangFont\ShzLeiJiJiangFont.fnt" />
    <None Include="Assets\OtherPackage\Spine\Runtime\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpritePixelLighting.cginc" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Text 1.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_ColorChange.shader" />
    <None Include="Assets\LZQ\Wei\Game_DT\Fonts\DT_Num2.fnt" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_EdgeColor.shader" />
    <None Include="Assets\NGUI\Resources\Shaders\Unlit - Transparent Colored 1.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_GrassFX.shader" />
    <None Include="Assets\2DxFX\Resources\_2dxFX_BlackHole.shader" />
    <None Include="Assets\WGM\XiYouZhengBa\2DObject\Font\Theme\time.fnt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ionic.Zlib">
      <HintPath>Assets\OtherPackage\xzhuan\HNFRW\Common\VoiceChat\Ionic.Zlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Debugger">
      <HintPath>Assets\Plugins\Debugger.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Debuger">
      <HintPath>Assets\LZQ\Wei\Game_BMW\Scripts\General\Debuger.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CString">
      <HintPath>Assets\Plugins\CString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniWebViewWP">
      <HintPath>Assets\Plugins\UniWebViewWP.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>Assets\Plugins\zxing.unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Compress.Info">
      <HintPath>Assets\Plugins\Compress.Info.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
